import{i as Z,g as ee}from"./index-DIXaVCf5.js";import{G as ae,_ as te,u as oe,r as s,o as se,d as c,b as _,c as w,e as t,f as l,w as r,h as d,y as F,j,k as f,l as M,F as U,t as Y,p as le,m as ne}from"./index-D4CjQwBG.js";import{l as re}from"./lodash-BnsPkmok.js";/* empty css                                                              */import"./index-CNS3ZdVi.js";const ie=n=>ae(n).format("YYYY-MM-DD HH:mm"),H=(n,m)=>n?n.find(g=>g.value===m):m,ce=()=>[{title:"项目名称",dataIndex:"name"},{title:"项目背景",dataIndex:"desc"},{title:"客户名称",dataIndex:"customer"},{title:"项目操作",dataIndex:"action",key:"action"},{title:"方案",dataIndex:"",key:"solution",width:"60%"}],de=()=>[{title:"名称",dataIndex:"name",key:"name"},{title:"场景",dataIndex:"topology",key:"topology"},{title:"求解目标",dataIndex:"targetExpr",key:"targetExpr"},{title:"创建时间",dataIndex:"createTime",key:"createTime"},{title:"状态",dataIndex:"status",key:"status",width:"100px"},{title:"操作",dataIndex:"action",key:"action"}],O=()=>[{label:"运行中",value:1,color:"processing"},{label:"成功",value:2,color:"success"},{label:"失败",value:3,color:"error"}],E=n=>(le("data-v-4eca6139"),n=n(),ne(),n),ue={class:"body_wrap"},pe={class:"p_wrap"},_e=E(()=>t("div",{class:"title_wrap"},null,-1)),me={class:"content_wrap",id:"content_wrap"},ve={class:"part_wrap"},fe={class:"p_title"},ge=E(()=>t("div",null,"容量测算",-1)),ye={class:"btn_wrap"},be={class:"table_wrap"},ke={key:0,class:"t_btn_wrap"},he=["onClick"],we=["onClick"],xe={key:0,class:"t_btn_wrap"},Ie=["onClick"],Ce=["onClick"],Se=E(()=>t("a",{href:"void:0",class:"a_item",disabled:""},"经济分析",-1)),Pe={__name:"index",setup(n){const m=oe(),g=s(!1),x=s(!1),z=s(),D=s([]),i=s({}),N=s({}),C=s(!1),T=s(!1),R=s(0),u=s({operator:"",pageSize:10,pageNumber:1}),G=()=>{m.push({name:"createProject"})},q=(a,e)=>{console.log("ceateSolution:",a,e);const p={projectId:a.id};(e==null?void 0:e.id)!==void 0&&(p.solutionId=e.id),m.push({name:"createProject",query:p})},K=a=>{x.value=!0,N.value=a,i.value=re.cloneDeep(a)},L=async()=>{const a=await z.value.validateFields();C.value=!0;const{code:e,msg:p}=await Z({id:N.value.id,...a});C.value=!1,e===0?(F.success("修改成功"),B()):F.error(p),x.value=!1},A=async(a=!0)=>{var k;g.value=a;const e={pageSize:u.value.pageSize,pageNumber:u.value.pageNumber};(k=u.value.operator)!=null&&k.trim()&&(e.operator=u.value.operator);const{msg:p,data:y,code:b}=await ee(e);console.log("code:",b,y),g.value=!1,b===0&&(D.value=y.result,R.value=y.total)},J=a=>{console.log("page:",a),u.value.pageNumber=a.current,u.value.pageSize=a.pageSize,A()},B=async()=>{A()};return se(()=>{B(),setTimeout(()=>{T.value=!0},5e3)}),(a,e)=>{const p=c("a-button"),y=c("a-tag"),b=c("a-table"),k=c("a-input"),S=c("a-form-item"),Q=c("a-textarea"),W=c("a-form"),X=c("a-modal");return _(),w("div",ue,[t("div",pe,[_e,t("div",me,[t("div",ve,[t("div",fe,[ge,t("div",ye,[l(p,{class:"btn_item",size2:"small",type:"primary",onClick:G},{default:r(()=>[j("新建项目")]),_:1})])]),t("div",be,[l(b,{borderd:"",class:"outer_table",loading:g.value,columns:d(ce)(),rowKey:"id","data-source":D.value,defaultExpandAllRows:T.value,onChange:J,pagination:{pageSize:u.value.pageSize,total:R.value,hideOnSinglePage:!1,showTotal:o=>`Total ${o} items`}},{bodyCell:r(({column:o,record:h})=>[o.key==="action"?(_(),w("div",ke,[t("a",{href:"void:0",class:"a_item",onClick:v=>q(h)},"新建方案",8,he),t("a",{href:"void:0",class:"a_item",onClick:v=>K(h)},"修改项目",8,we)])):f("",!0),o.key==="solution"?(_(),M(b,{key:1,class:"inner_table",showHeader:!0,columns:d(de)(),"data-source":h.solutions,pagination:!1,size:"small",defaultExpandAllRows:!0},{bodyCell:r(({column:v,text:P,record:V})=>{var $;return[v.key==="action"?(_(),w("div",xe,[t("a",{href:"void:0",class:"a_item",onClick:I=>d(m).push({name:"projectDetail",query:{projectId:h.id,solutionId:V.id}})},"查看",8,Ie),t("a",{href:"void:0",class:"a_item",onClick:I=>q(h,V)},"修改方案",8,Ce),Se])):f("",!0),v.key==="createTime"?(_(),w(U,{key:1},[j(Y(d(ie)(P)),1)],64)):f("",!0),v.key==="status"?(_(),M(y,{key:2,color:($=d(H)(d(O)(),P))==null?void 0:$.color},{default:r(()=>{var I;return[j(Y((I=d(H)(d(O)(),P))==null?void 0:I.label),1)]}),_:2},1032,["color"])):f("",!0),v.key==="topology"?(_(),w(U,{key:3},[],64)):f("",!0)]}),_:2},1032,["columns","data-source"])):f("",!0)]),_:1},8,["loading","columns","data-source","defaultExpandAllRows","pagination"])])])])]),l(X,{open:x.value,"onUpdate:open":e[3]||(e[3]=o=>x.value=o),title:"修改项目",onOk:L,"confirm-loading":C.value},{default:r(()=>[t("div",null,[l(W,{labelAlign:"left2",ref_key:"formRef",ref:z,model:i.value,name:"basic","label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:r(()=>[l(S,{label:"项目名称",name:"name",rules:[{required:!0,message:"Please input!"}]},{default:r(()=>[l(k,{value:i.value.name,"onUpdate:value":e[0]||(e[0]=o=>i.value.name=o)},null,8,["value"])]),_:1}),l(S,{label:"客户名称",name:"customer",rules:[{required:!0,message:"Please input!"}]},{default:r(()=>[l(k,{value:i.value.customer,"onUpdate:value":e[1]||(e[1]=o=>i.value.customer=o)},null,8,["value"])]),_:1}),l(S,{label:"项目背景",name:"desc",rules:[{required:!0,message:"Please input!"}]},{default:r(()=>[l(Q,{value:i.value.desc,"onUpdate:value":e[2]||(e[2]=o=>i.value.desc=o)},null,8,["value"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["open","confirm-loading"])])}}},Te=te(Pe,[["__scopeId","data-v-4eca6139"]]);export{Te as default};
