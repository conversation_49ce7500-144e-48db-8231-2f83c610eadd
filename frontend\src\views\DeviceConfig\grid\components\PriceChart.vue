<template>
  <div class="price-chart" ref="chartRef"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['select'])

const chartRef = ref()
let chartInstance = null

const typeColors = {
  1: '#52c41a', // 深谷 - 绿色
  2: '#1890ff', // 低谷 - 蓝色  
  3: '#faad14', // 平时 - 黄色
  4: '#ff7a45', // 高峰 - 橙色
  5: '#f5222d'  // 尖峰 - 红色
}

const typeNames = {
  1: '深谷',
  2: '低谷',
  3: '平时', 
  4: '高峰',
  5: '尖峰'
}

// 窗口大小变化处理函数
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)

  chartInstance.on('click', (params) => {
    emit('select', {
      hour: params.dataIndex,
      data: params.data
    })
  })

  updateChart()
}

const updateChart = () => {
  if (!chartInstance) return
  
  // 生成24小时数据
  const hours = Array.from({ length: 24 }, (_, i) => i)
  
  // 为每个时段创建单独的series
  const seriesData = []
  
  props.data.forEach((period, index) => {
    const periodData = hours.map(hour => {
      if (hour >= period.begin && hour <= period.end) {
        return period.price
      }
      return null
    })
    
    seriesData.push({
      name: typeNames[period.type],
      type: 'line',
      data: periodData,
      step: 'end',
      lineStyle: {
        width: 2,
        color: typeColors[period.type]
      },
      areaStyle: {
        color: typeColors[period.type] + '40'
      },
      symbol: 'none',
      connectNulls: false
    })
  })
  
  const option = {
    title: {
      // text: '24小时电价配置',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const hour = params[0].dataIndex
        const period = props.data.find(p => hour >= p.begin && hour <= p.end)
        const validParam = params.find(p => p.value !== null)
        return `
          <div>
            <div>时间: ${hour}:00-${hour + 1}:00</div>
            <div>类型: ${period ? typeNames[period.type] : '未配置'}</div>
            <div>价格: ${validParam ? validParam.value : 0} 元/kWh</div>
          </div>
        `
      }
    },
    legend: {
      show: true,
      top: '8%'
    },
    xAxis: {
      type: 'category',
      data: hours.map(h => `${h}:00`),
      axisLabel: {
        interval: 1,
        rotate: 45
      },
      boundaryGap: false
    },
    yAxis: {
      type: 'value',
      name: '价格(元/kWh)',
      min: 0
    },
    series: seriesData
  }
  
  chartInstance.setOption(option, true)
}

watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

onMounted(() => {
  nextTick(() => {
    initChart()
  })

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)

  // 销毁图表实例
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

// 暴露更新方法
defineExpose({
  updateChart,
  resize: handleResize
})
</script>

<style scoped>
.price-chart {
  width: 100%;
  height: 100%;
  min-height: 400px;
}
</style>





