import{_ as Ce,r as m,z as Ie,v as qe,d as y,b as o,l as f,w as d,e,c as h,i as q,F as z,k as M,f as c,j as V,A as N,t as P,u as Ze,s as He,x as Je,o as Qe,y as ne,h as $,B as Xe,C as Ye,D as J,E as Q,p as ea,m as aa}from"./index-D4CjQwBG.js";import{L as ke,_ as la,c as ta,a as sa,b as na,d as oa,e as ua}from"./index-MQ9bg0vR.js";import{b as ia,c as ra,d as xe,e as re,f as da,h as ca}from"./index-DIXaVCf5.js";import"./index-CNS3ZdVi.js";import{l as _a}from"./lodash-BnsPkmok.js";/* empty css                                                              */const va=F=>[{label:"客户名称",name:"customer",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"string"},{label:"项目名称",name:"projectName",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"string"},{label:"项目周期",name:"cycle",unit:"年",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"年产氢量",name:"h2Product",unit:"kg",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"贷款利率",name:"loanRate",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",max:1},{label:"贷款周期",name:"loanCycle",unit:"年",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"贷款比例",name:"loanRadio",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"项目描述",name:"desc",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"textarea"}],ma=()=>[{label:"LCOH",name:"target0",unit:"",default:1,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"投资成本最低",name:"target1",unit:"",default:0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"弃电率最低",name:"target2",unit:"",default:0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"产氢量最大",name:"target3",unit:"",default:0,rules:[{required:!1,message:"请输入"}],type:"number"}],pa=()=>[{label:"快速测算",name:"",default:1}],fa=()=>[{label:"用水价格",name:"water_price",unit:"元/吨",default:void 0,rules:[{required:!0,message:"请输入"}],type:"select",options:[{label:"10.1元/吨",value:10.1},{label:"11.1元/吨",value:11.1}]},{label:"制氢耗水量",name:"h2_water_consuming",unit:"L/Nm³",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"制氢策略",name:"ele_policy",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"select",options:[{label:"满功率",value:1}]}],ga=()=>[{title:"型号",dataIndex:"name"},{title:"容量",dataIndex:"capacity"},{title:"价格",dataIndex:"price"},{title:"电耗(kWh/Nm³)",dataIndex:"power_consumption"},{title:"额定功率(kw)",dataIndex:"pe"},{title:"最低负载率",dataIndex:"lower_load_rate"},{title:"最高负载率",dataIndex:"upper_load_rate"},{title:"年衰减曲线",dataIndex:"damp_curve"},{title:"制氢电源效率",dataIndex:"power_supply_efficiency"},{title:"辅助系统能耗(kWh/Nm³)",dataIndex:"assist_consumption"}],ba=()=>[{label:"年下电比例",name:"grid_down_radio",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",max:1},{label:"绿电最大上网比例",name:"grid_up_radio",unit:"",default:.2,rules:[{required:!1,message:"请输入"}],type:"number",max:1},{label:"绿电上网价格",name:"grid_sale_price",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"select",options:[{label:"0.5元/kwh",value:.5},{label:"0.51元/kwh",value:.51}]}],ya=()=>[{label:"控制策略",name:"es_policy",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"select",options:[{label:"削峰填谷",value:1}]}],ha=()=>[{title:"单机功率(MW)",dataIndex:"single_power"},{title:"充电效率",dataIndex:"charge_efficiency"},{title:"放电效率",dataIndex:"discharge_efficiency"},{title:"充放电倍率",dataIndex:"c_rate"},{title:"储能占比",dataIndex:"radio"},{title:"初始SOC",dataIndex:"init_soc"},{title:"SOC下限",dataIndex:"min_soc"},{title:"SOC上限",dataIndex:"max_soc"},{title:"置信度",dataIndex:"confidence"},{title:"寿命(年)",dataIndex:"life_cycle"}],wa=()=>[{label:"储罐容量下限",name:"hs_min_capacity",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"储罐容量上限",name:"hs_max_capacity",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"最大升负荷速率",name:"max_increase_load_rate",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"最大降负荷速率",name:"max_down_load_rate",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"负荷调整时间",name:"adjust_time",unit:"min",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"负荷调节间隔",name:"adjust_interval",unit:"min",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"}],ka=()=>[{title:"体积(m³)",dataIndex:"volume"},{title:"最低运行压力",dataIndex:"min_pressure"},{title:"最大运行压力",dataIndex:"max_pressure"},{title:"价格",dataIndex:"price"},{title:"占地面积(㎡)",dataIndex:"area"}],xa=()=>[{label:"光伏EPC投资",name:"pv_epc",unit:"元/W",default:2.9,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"风电EPC投资",name:"wind_epc",unit:"元/W",default:4.2,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"储能EPC投资",name:"es_epc",unit:"元/W",default:1.3,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"制氢系统投资",name:"h2_invest",unit:"元/W",default:2,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"制氢厂房投资",name:"plant_invest",unit:"元/W",default:1.5,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"储氢系统投资",name:"hs_invest",unit:"元/W",default:2,rules:[{required:!0,message:"请输入"}],type:"number"}],Ca=()=>[{label:"设备折现率",name:"discount_rate",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number",max:1},{label:"负荷缺失率",name:"负荷缺失率",unit:"",default:0,rules:[{required:!1,message:"请输入"}],type:"number",max:1},{label:"光伏运维比例",name:"pv_om_radio",unit:"",default:.25,rules:[{required:!0,message:"请输入"}],type:"number",max:1},{label:"光伏运营成本",name:"pv_om_cost",unit:"元/W/年",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"风电运维比例",name:"wind_om_radio",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number",max:1},{label:"风电运营成本",name:"wind_om_cost",unit:"元/W/年",default:.3,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"储能运维比例",name:"es_om_radio",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number",max:1},{label:"储能运营成本",name:"es_om_cost",unit:"元/W/年",default:.0675,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"制氢运维比例",name:"h2_om_radio",unit:"",default:.02,rules:[{required:!0,message:"请输入"}],type:"number",max:1},{label:"制氢运营成本",name:"h2_om_cost",unit:"元/W/年",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"储氢运维比例",name:"hs_om_radio",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number",max:1},{label:"储氢运维成本",name:"hs_om_cost",unit:"元/Nm³/年",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number"}],Ia={projectName:"tmp名字",customer:"tmp customer",desc:"tmp desc",h2Product:1.2,cycle:1,loanRadio:.25,loanCycle:25,loanRate:.2,solutionName:"方案名",topology:[1,1,0,1,,0],targetExpr:[.3,.2,0,.5],algorithm:1,pv_min_capacity:0,pv_max_capacity:50,pv_dev_ids:[1],pv_damp_rate:[],wind_min_capacity:0,wind_max_capacity:66,grid_down_radio:.1,grid_zone_id:2,grid_year:2025,grid_sale_price:.5,grid_up_radio:.2,es_dev_ids:[],es_min_capacity:0,es_max_capacity:77,es_policy:1,ele_dev_ids:[],ele_min_capacity:0,ele_max_capacity:88,ele_policy:1,water_price:10,h2_water_consuming:12,hs_dev_ids:[],max_increase_load_rate:.5,max_down_load_rate:.36,adjust_time:12,adjust_interval:5,absorb_id:2,pv_forecast_zone_id:2,pv_forecast:[],wind_forecast_zone_id:1,wind_forecast:[],pv_epc:2.9,wind_epc:10,es_epc:11,h2_invest:12,plant_invest:13,hs_invest:38,discount_rate:.4,pv_om_radio:.6,pv_om_cost:.55,wind_om_radio:.332,wind_om_cost:5,es_om_radio:.331,es_om_cost:8,h2_om_radio:.33,h2_om_cost:7,hs_om_radio:.332,hs_om_cost:5},qa={class:"power_list"},za=["onClick"],Ua={class:"title"},Sa={class:"title"},Pa={__name:"index",props:["options","chartList","config"],emits:["submit"],setup(F,{expose:oe,emit:X}){const W=F,t=m([40,50]),b=m({pvPower:[]});m(1);const U=m({});m([]);const A=m([]),E=m({desc:"自定义地区",id:-1,title:"",series:[{data:[],type:"line",lineStyle:{normal:{color:"#1677ff"}}}],grid:{top:"5%",bottom:"10%",left:"3%",right:"5%",containLabel:!0},dataZoom:W.config.hasZoom?[{show:!0,realtime:!0,start:t.value[0],end:t.value[1],bottom:"0px"},{type:"inside"}]:null}),Y=_a.cloneDeep(E.value),T=m(!1),G=X,ee=R=>{U.value=R};oe({open:()=>{T.value=!0},close:()=>{T.value=!1}});const _e=()=>{G("submit",U.value)};Ie(()=>W.chartList,R=>{});const ie=qe(()=>{var R;return U.value=W.config.default||{},(R=W.chartList)==null?void 0:R.map(I=>{const{yData:D,unit:O,desc:B,id:ae}=I;return{desc:B,id:ae,title:"",series:[{data:D,type:"line",lineStyle:{normal:{color:"#1677ff"}}}],grid:{top:"5%",bottom:"10%",left:"3%",right:"5%",containLabel:!0},dataZoom:W.config.hasZoom?[{show:!0,realtime:!0,start:t.value[0],end:t.value[1],bottom:"0px"},{type:"inside"}]:null}})}),ve=R=>{const I=new FileReader;A.value=[],E.value=Y,I.onload=D=>{D.target.result.split(/\r*\n+/).forEach(B=>{B&&A.value.push(parseFloat(B))}),console.log("file content:",A.value),E.value={...E.value,series:{...E.value.series[0],data:A.value}},U.value.id==-1&&(U.value.series[0].data=A.value),R.onSuccess()},I.readAsText(R.file)};return(R,I)=>{const D=y("UploadOutlined"),O=y("a-button"),B=y("a-upload"),ae=y("a-modal");return o(),f(ae,{width:"90%",open:T.value,"onUpdate:open":I[2]||(I[2]=Z=>T.value=Z),title:W.config.modalTitle,onOk:_e},{default:d(()=>{var Z;return[e("div",qa,[(Z=ie.value)!=null&&Z.length?(o(!0),h(z,{key:0},q(ie.value,L=>(o(),h("div",{class:N(["p_item",{p_item_sel:L.id===U.value.id}]),onClick:le=>ee(L)},[c(ke,{data:L,class:"chart_wrap"},null,8,["data"]),e("div",Ua,P(L.desc),1)],10,za))),256)):M("",!0),W.config.canUploadSelf?(o(),h("div",{key:1,class:N(["p_item",{p_item_sel:U.value.id===-1}]),onClick:I[1]||(I[1]=L=>ee(E.value))},[c(ke,{data:E.value,class:"chart_wrap"},null,8,["data"]),e("div",Sa,[c(B,{onChange2:R.uploadChange,"before-upload2":L=>R.beforeUpload(L),customRequest:ve,onRemove:I[0]||(I[0]=()=>{b.value.pvPower=[]}),accept:".txt",maxCount:1},{default:d(()=>[c(O,{size:"small",style:{fontSize:"11px",marginRight:"10px"}},{icon:d(()=>[c(D)]),default:d(()=>[V(" 上传数据 ")]),_:1})]),_:1},8,["onChange2","before-upload2"]),V(" 自定义数据 ")])],2)):M("",!0)])]}),_:1},8,["open","title"])}}},de=Ce(Pa,[["__scopeId","data-v-2d44c3d8"]]),v=F=>(ea("data-v-2c1da801"),F=F(),aa(),F),Va={class:"body_wrap"},Ra={class:"part_wrap"},$a={class:"content_wrap"},Da={class:"box_wrap"},La=v(()=>e("div",{class:"b_title"},"项目信息",-1)),Ka={class:"b_body"},Wa={class:"line_item"},ja={class:"box_wrap"},Aa=v(()=>e("div",{class:"b_title"},"求解方式",-1)),Ea={class:"b_body"},Ma={class:"goal_list"},Na=v(()=>e("div",{class:"g_title"},"求解目标",-1)),Ta=["onClick","name","rules"],Ba={class:"goal_item"},Fa={class:"name"},Oa=v(()=>e("div",{class:"v_line"},"|",-1)),Ga={class:"number_input"},Za=v(()=>e("div",{class:"g_tips"},"选择多个求解目标后，需要在每个目标内设置求解目标权重",-1)),Ha={class:"goal_list"},Ja=v(()=>e("div",{class:"g_title"},"求解算法",-1)),Qa={class:"box_wrap"},Xa=v(()=>e("div",{class:"b_title"},"场景选择",-1)),Ya={class:"b_body"},el={class:"scene_title_wrap"},al={class:"pv_wind_wrap"},ll=v(()=>e("div",{class:"left"},[e("img",{src:la})],-1)),tl=v(()=>e("div",{class:"right"},[e("div",{class:"r_title"},"光伏"),e("div",{class:"r_desc"},"选择光伏场景后可配置光伏参数")],-1)),sl=[ll,tl],nl=v(()=>e("div",{class:"left"},[e("img",{src:ta})],-1)),ol=v(()=>e("div",{class:"right"},[e("div",{class:"r_title"},"风机"),e("div",{class:"r_desc"},"选择风机场景后可配置风机参数")],-1)),ul=[nl,ol],il=v(()=>e("div",{class:"left"},[e("img",{src:sa})],-1)),rl=v(()=>e("div",{class:"right"},[e("div",{class:"r_title"},"电网"),e("div",{class:"r_desc"},"选择电网场景后可配置电网用电")],-1)),dl=[il,rl],cl=v(()=>e("div",{class:"left"},[e("img",{src:na})],-1)),_l=v(()=>e("div",{class:"right"},[e("div",{class:"r_title"},"储能"),e("div",{class:"r_desc"},"选择储能场景后可配置储能参数")],-1)),vl=[cl,_l],ml=v(()=>e("div",{class:"left"},[e("img",{src:oa})],-1)),pl=v(()=>e("div",{class:"right"},[e("div",{class:"r_title"},"制氢"),e("div",{class:"r_desc"},"选择制氢场景后可配置制氢参数")],-1)),fl=[ml,pl],gl=v(()=>e("div",{class:"left"},[e("img",{src:ua})],-1)),bl=v(()=>e("div",{class:"right"},[e("div",{class:"r_title"},"储氢"),e("div",{class:"r_desc"},"选择储氢场景后可配置储氢参数")],-1)),yl=[gl,bl],hl={class:"scene_content_wrap"},wl={class:"k_v_list"},kl={class:"k_v_item"},xl=v(()=>e("div",{class:"label"},"出力曲线:",-1)),Cl={class:"value"},Il={class:"desc_wrap"},ql={class:"k_v_item"},zl=v(()=>e("div",{class:"label"},"光伏年衰减率:",-1)),Ul={class:"value"},Sl={class:"desc_wrap"},Pl={class:"k_v_item"},Vl=v(()=>e("div",{class:"label"},"容量范围(MW):",-1)),Rl={class:"value range_item"},$l=v(()=>e("div",{class:"middle_line"},"—",-1)),Dl={class:"k_v_list"},Ll={class:"k_v_item"},Kl=v(()=>e("div",{class:"label"},"出力曲线:",-1)),Wl={class:"value"},jl={class:"desc_wrap"},Al={class:"k_v_item"},El=v(()=>e("div",{class:"label"},"容量范围(MW):",-1)),Ml={class:"value range_item"},Nl=v(()=>e("div",{class:"middle_line"},"—",-1)),Tl={class:"line_item"},Bl=v(()=>e("a",{href:"#"},"Change city",-1)),Fl={class:"line_item"},Ol={class:"range_item"},Gl=v(()=>e("div",{class:"middle_line"},"—",-1)),Zl={class:"common_form_item"},Hl=v(()=>e("div",{class:"i_label"},"储能电池选择",-1)),Jl={class:"line_item"},Ql={class:"range_item"},Xl=v(()=>e("div",{class:"middle_line"},"—",-1)),Yl={class:"common_form_item"},et=v(()=>e("div",{class:"i_label"},"电解槽选择",-1)),at={class:"line_item"},lt={class:"k_v_list"},tt={class:"k_v_item",style:{margin:"0 0 10px 0"}},st=v(()=>e("div",{class:"label"},"供氢曲线",-1)),nt={class:"value"},ot={class:"desc_wrap"},ut={class:"common_form_item"},it=v(()=>e("div",{class:"i_label"},"储氢罐选择",-1)),rt={class:"box_wrap"},dt=v(()=>e("div",{class:"b_title"},"成本配置",-1)),ct={class:"b_body"},_t=v(()=>e("div",{class:"b_title2"},"投资成本",-1)),vt={class:"line_item"},mt=v(()=>e("div",{class:"b_title2"},"运营成本",-1)),pt={class:"line_item"},ft={class:"button_wrap"},gt={__name:"index",setup(F){const oe=Ze(),X=He(),W=m();m(!1);const t=m({}),b=Je({selectedAlkRowKeys:[],selectedBatRowKeys:[],loading:!1,running:!1}),U=m({alk:[],bat:[],alkStorage:[]}),A=m([{title:"光伏",key:0},{title:"风机",key:1},{title:"电网",key:2},{title:"储能",key:3},{title:"制氢",key:4},{title:"储氢",key:5}]),E=m("新建项目"),Y=m(),T=m(),G=m({}),ee=m({}),ue=m({}),ce=m({}),_e=s=>{var l,i;console.log("pv power:",s),Y.value.close(),ue.value=s,t.value.pv_forecast_zone_id=s.id,s.id==-1&&(t.value.pv_forecast=((i=(l=s==null?void 0:s.series)==null?void 0:l[0])==null?void 0:i.data)||[])},ie=s=>{console.log("pv device:",s),T.value.close(),ce.value=s,t.value.pv_dev_ids=[s.id]},ve=async()=>{const{code:s,msg:l,data:i}=await xe({type:1});G.value=i.result.map(p=>{const{zone:u,forecast:_}=p,{id:w,region:g,country:k,province:j,city:x}=u;return{yData:_.data,unit:"",desc:[g,k,j,x].filter(C=>!!C).join("_"),id:w}})},R=async()=>{const{code:s,msg:l,data:i}=await re({type:1});ee.value=i.result.map(p=>{const{baseInfo:{id:u,manufacturer:_,model:w},params:g}=p;return{yData:g.damp_curve,unit:"",desc:[_,w].filter(k=>!!k).join("_"),id:u}}),console.log("pv forecast:",G.value)},I=m();m();const D=m({});m({});const O=m({});m({});const B=s=>{var l,i;console.log("wind power:",s),I.value.close(),O.value=s,t.value.wind_forecast_zone_id=s.id,s.id==-1&&(t.value.wind_forecast=((i=(l=s==null?void 0:s.series)==null?void 0:l[0])==null?void 0:i.data)||[])},ae=async()=>{const{code:s,msg:l,data:i}=await xe({type:2});D.value=i.result.map(p=>{const{zone:u,forecast:_}=p,{id:w,region:g,country:k,province:j,city:x}=u;return{yData:_.data,unit:"",desc:[g,k,j,x].filter(C=>!!C).join("_"),id:w}}),console.log("wind forecast:",D.value)},Z=s=>{te.value[s]=!te.value[s],t.value.targetExpr=te.value.map((l,i)=>l?t.value[`target${i}`]:0)},L=m([]),le=m(4),te=m([!1,!1,!1,!1]),S=m([!1,!1,!1,!1,!1,!1]),H=s=>{S.value[s]=!S.value[s],t.value.topology=S.value.map((l,i)=>l?1:0),L.value=A.value.filter((l,i)=>S.value[i]),S.value[s]&&(le.value=s)};qe(()=>b.selectedAlkRowKeys.length>0);const ze=s=>{console.log("selectedAlkRowKeys changed: ",s),b.selectedAlkRowKeys=s,t.value.ele_dev_ids=s},Ue=async()=>{const{code:s,msg:l,data:i}=await re({type:4});U.value.alk=i.result.map(p=>{const{baseInfo:u,params:_}=p;return{...u,..._}})},me=m([]),pe=m({}),Se=async()=>{const{code:s,msg:l,data:i}=await da();me.value=i.result.map(p=>{const{price:u,zone:{city:_,country:w,id:g,province:k,region:j}}=p;return{label:[j,w,k,_].filter(x=>!!x).join("_"),value:g,children:Object.keys(u).map(x=>{const C=parseInt(x);return{label:C,value:C}})}}),console.log("grid price:",i.result,me.value)},Pe=(s,l)=>{console.log("slect price:",s,l),t.value.grid_zone_id=s[0],t.value.grid_year=s[1],pe.value=l},Ve=async()=>{const{code:s,msg:l,data:i}=await re({type:3});U.value.bat=i.result.map(p=>{const{baseInfo:u,params:_}=p;return{...u,..._}})},Re=s=>{console.log("selectedBatRowKeys changed: ",s),b.selectedBatRowKeys=s,t.value.es_dev_ids=s},be=m([]),fe=m(),ye=m({}),$e=async()=>{const{code:s,msg:l,data:i}=await re({type:5});U.value.alkStorage=i.result.map(p=>{const{baseInfo:u,params:_}=p;return{...u,..._}})},De=s=>{console.log("alk storage:",s),fe.value.close(),ye.value=s,t.value.absorb_id=s.id},Le=s=>{console.log("selectedAlkStoreRowKeys changed: ",s),b.selectedAlkStoreRowKeys=s,t.value.hs_dev_ids=s},Ke=async()=>{const{code:s,msg:l,data:i}=await ca();be.value=i.result.map(p=>{const{id:u,name:_,demandCurve:w}=p;return{yData:w.data,unit:"",desc:_,id:u}}),console.log("wind forecast:",D.value)},We=()=>{var u,_;if(t.value.targetExpr.reduce((w,g)=>w+g)!==1)return ne.warn("已选求解目标权重总和需为1 !"),!1;const l=t.value.pv_forecast_zone_id>0||((u=t.value.pv_forecast)==null?void 0:u.length),i=t.value.wind_forecast_zone_id>0||((_=t.value.wind_forecast)==null?void 0:_.length);return l||i?!0:(ne.warn("光伏和风机出力曲线请至少选择1项!"),!1)},je=async()=>{const s=await W.value.validateFields(),l={...s,...t.value};if(["target0","target1","target2","target3","scene0","scene1","scene2","scene3","scene4","scene5"].forEach(g=>l.hasOwnProperty(g)&&delete l[g]),!We())return;console.log("re:",l,s,t.value),b.loading=!0,b.running=!0;const{code:u,msg:_,data:w}=await ia(l);b.running=!1,b.loading=!1,u===0?ne.success("已提交运行"):ne.error(_),oe.push({name:"projectDetail",query:{taskId:w.taskId}})},Ae=()=>{t.value=Ia},Ee=()=>{const{targetExpr:s,topology:l}=t.value,i=["target0","target1","target2","target3"],p=["scene0","scene1","scene2","scene3","scene4","scene5"];i.forEach((u,_)=>{t.value[u]=s[_],te.value[_]=!!s[_]}),p.forEach((u,_)=>{t.value[u]=l[_],S.value[_]=!!l[_]}),L.value=A.value.filter((u,_)=>S.value[_]),le.value=A.value.findIndex((u,_)=>S.value[_])};Ie(X,s=>{console.log("query:",s)});const Me=async()=>{Ae();const{projectId:s,solutionId:l}=X.query;(s||l)&&(b.loading=!0),await ve(),await R(),await ae(),Se(),Ue(),Ve(),$e(),Ke(),l&&(b.loading=!1)},Ne=async()=>{var j,x;const{projectId:s,solutionId:l}=X.query;if(s==null){console.log("Pure create project");return}const i={};let p={};i.projectId=parseInt(s),l&&(i.solutionId=parseInt(l)),b.loading=!0;const{code:u,msg:_,data:{project:w,solution:g,calcParams:k}}=await ra(i);if(b.loading=!1,p={...w,projectName:w.name},l){const{targetExpr:C,topology:K}=g;p={...p,...g,...k,target0:C[0],target1:C[1],target2:C[2],target3:C[3],scene0:K[0],scene1:K[1],scene2:K[2],scene3:K[3],scene4:K[4],scene5:K[5]},ue.value={desc:(j=G.value.find(se=>se.id===k.pv_forecast_zone_id))==null?void 0:j.desc},O.value={desc:(x=D.value.find(se=>se.id===k.wind_forecast_zone_id))==null?void 0:x.desc},console.log("rrr:",k.es_dev_ids),b.selectedBatRowKeys=k.es_dev_ids,b.selectedAlkRowKeys=k.ele_dev_ids}console.log("enter params:",p),u==0?t.value=p:ne.error(_)};return Qe(async()=>{await Me(),await Ne(),Ee()}),(s,l)=>{const i=y("a-breadcrumb-item"),p=y("a-breadcrumb"),u=y("a-input-number"),_=y("a-input"),w=y("a-textarea"),g=y("a-form-item"),k=y("a-radio-button"),j=y("a-radio-group"),x=y("a-button"),C=y("a-select-option"),K=y("a-select"),se=y("a-cascader"),ge=y("a-table"),Te=y("a-tab-pane"),Be=y("a-tabs"),Fe=y("a-form"),Oe=y("a-spin");return o(),h("div",Va,[e("div",Ra,[e("div",null,[c(p,null,{default:d(()=>[c(i,null,{default:d(()=>[e("a",{href:"void:0",onClick:l[0]||(l[0]=n=>$(oe).back())}," < 返回")]),_:1}),c(i,null,{default:d(()=>[V(P(E.value),1)]),_:1})]),_:1})]),e("div",$a,[c(Oe,{spinning:b.loading},{default:d(()=>[c(Fe,{ref_key:"formRef",ref:W,labelAlign:"left",model:t.value,name:"basic","label-col":{span:14},"wrapper-col":{span:12},autocomplete:"off"},{default:d(()=>[e("div",Da,[La,e("div",Ka,[e("div",Wa,[(o(!0),h(z,null,q($(va)(),n=>(o(),f(g,{class:"line_form_item",label:n.unit?`${n.label}(${n.unit})`:n.label,name:n.name,rules:n.rules},{default:d(()=>[n.type==="number"?(o(),f(u,{key:0,style:{width:"80%"},class:"input_deal_wrap",defaultValue:n.default,value:t.value[n.name],"onUpdate:value":a=>t.value[n.name]=a,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):n.type==="string"?(o(),f(_,{key:1,style:{width:"80%"},class:"input_deal_wrap",defaultValue:n.default,value:t.value[n.name],"onUpdate:value":a=>t.value[n.name]=a,size:"small"},null,8,["defaultValue","value","onUpdate:value"])):n.type==="textarea"?(o(),f(w,{key:2,style:Xe([{width:"80%"},{margin:"5px 0 5px 0"}]),class:"input_deal_wrap",autosize:{maxRows:3},defaultValue:n.default,value:t.value[n.name],"onUpdate:value":a=>t.value[n.name]=a,size:"small"},null,8,["defaultValue","value","onUpdate:value"])):M("",!0)]),_:2},1032,["label","name","rules"]))),256))])])]),e("div",ja,[Aa,e("div",Ea,[e("div",Ma,[Na,(o(!0),h(z,null,q($(ma)(),(n,a)=>(o(),h("div",{onClick:r=>Z(a),name:n.name,rules:n.rules,class:N({sel_item:te.value[a]})},[e("div",Ba,[e("div",Fa,P(n.label),1),Oa,e("div",Ga,[c(u,{controls:!1,onClick:l[1]||(l[1]=r=>{r.stopPropagation()}),onStep:l[2]||(l[2]=Ye(r=>r.stopPropagation(),["prevent","stop"])),class:"input_deal_wrap",defaultValue:n.default,value:t.value[n.name],"onUpdate:value":r=>t.value[n.name]=r,size:"small",min:0,max:1},null,8,["defaultValue","value","onUpdate:value"])])])],10,Ta))),256))]),Za,e("div",Ha,[Ja,e("div",null,[c(j,{size:"small",value:t.value.algorithm,"onUpdate:value":l[3]||(l[3]=n=>t.value.algorithm=n),"button-style":"solid"},{default:d(()=>[(o(!0),h(z,null,q($(pa)(),n=>(o(),f(k,{value:n.default},{default:d(()=>[V(P(n.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["value"])])])])]),e("div",Qa,[Xa,e("div",Ya,[e("div",el,[e("div",al,[e("div",{class:N(["s_t_item_sp",{s_t_item_sel:S.value[0]}]),onClick:l[4]||(l[4]=n=>H(0))},sl,2),e("div",{class:N(["s_t_item_sp",{s_t_item_sel:S.value[1]}]),onClick:l[5]||(l[5]=n=>H(1))},ul,2)]),e("div",{class:N(["s_t_item",{s_t_item_sel:S.value[2]}]),onClick:l[6]||(l[6]=n=>H(2))},dl,2),e("div",{class:N(["s_t_item",{s_t_item_sel:S.value[3]}]),onClick:l[7]||(l[7]=n=>H(3))},vl,2),e("div",{class:N(["s_t_item",{s_t_item_sel:S.value[4]}]),onClick:l[8]||(l[8]=n=>H(4))},fl,2),e("div",{class:N(["s_t_item",{s_t_item_sel:S.value[5]}]),onClick:l[9]||(l[9]=n=>H(5))},yl,2)]),e("div",hl,[c(Be,{activeKey:le.value,"onUpdate:activeKey":l[23]||(l[23]=n=>le.value=n)},{default:d(()=>[(o(!0),h(z,null,q(L.value,n=>(o(),f(Te,{key:n.key,tab:n.title},{default:d(()=>[J(e("div",wl,[e("div",kl,[xl,e("div",Cl,[c(x,{type:"primary",size:"small",onClick:l[10]||(l[10]=a=>Y.value.open())},{default:d(()=>[V("请选择")]),_:1}),e("div",Il,P(ue.value.desc),1)])]),e("div",ql,[zl,e("div",Ul,[c(x,{type:"primary",size:"small",onClick:l[11]||(l[11]=a=>T.value.open())},{default:d(()=>[V("请选择")]),_:1}),e("div",Sl,P(ce.value.desc),1)])]),e("div",Pl,[Vl,e("div",Rl,[c(u,{class:"input_deal_wrap2",defaultValue:0,value:t.value.pv_min_capacity,"onUpdate:value":l[12]||(l[12]=a=>t.value.pv_min_capacity=a),size:"small",min:0},null,8,["value"]),$l,c(u,{class:"input_deal_wrap2",value:t.value.pv_max_capacity,"onUpdate:value":l[13]||(l[13]=a=>t.value.pv_max_capacity=a),size:"small",min:0},null,8,["value"])])])],512),[[Q,n.key===0]]),J(e("div",Dl,[e("div",Ll,[Kl,e("div",Wl,[c(x,{type:"primary",size:"small",onClick:l[14]||(l[14]=a=>I.value.open())},{default:d(()=>[V("请选择")]),_:1}),e("div",jl,P(O.value.desc),1)])]),e("div",Al,[El,e("div",Ml,[c(u,{class:"input_deal_wrap2",value:t.value.wind_min_capacity,"onUpdate:value":l[15]||(l[15]=a=>t.value.wind_min_capacity=a),size:"small",min:0},null,8,["value"]),Nl,c(u,{class:"input_deal_wrap2",value:t.value.wind_max_capacity,"onUpdate:value":l[16]||(l[16]=a=>t.value.wind_max_capacity=a),size:"small",min:0},null,8,["value"])])])],512),[[Q,n.key===1]]),J(e("div",null,[e("div",Tl,[(o(!0),h(z,null,q($(ba)(),a=>(o(),f(g,{class:"line_form_item",label:a.unit?`${a.label}(${a.unit})`:a.label,name:a.name,rules:a.rules},{default:d(()=>[a.type==="number"?(o(),f(u,{key:0,class:"input_deal_wrap",defaultValue:a.default,value:t.value[a.name],"onUpdate:value":r=>t.value[a.name]=r,size:"small",min:0,max:a.max},null,8,["defaultValue","value","onUpdate:value","max"])):a.type==="select"?(o(),f(K,{key:1,ref_for:!0,ref:"select",value:t.value[a.name],"onUpdate:value":r=>t.value[a.name]=r,style:{width:"120px"},onChange:s.handleChange},{default:d(()=>[(o(!0),h(z,null,q(a.options,r=>(o(),f(C,{value:r.value},{default:d(()=>[V(P(r.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value","onChange"])):M("",!0)]),_:2},1032,["label","name","rules"]))),256)),c(g,{class:"line_form_item",label:"网购电价",name:"item.name",rules:"item.rules"},{default:d(()=>{var a,r,he,we;return[e("div",null,[V(P((r=(a=pe.value)==null?void 0:a[0])==null?void 0:r.label)+", "+P((we=(he=pe.value)==null?void 0:he[1])==null?void 0:we.label)+" ",1),c(se,{value:s.value,"onUpdate:value":l[17]||(l[17]=Ge=>s.value=Ge),placeholder:"Please select",options:me.value,onChange:Pe},{default:d(()=>[Bl]),_:1},8,["value","options"])])]}),_:1})])],512),[[Q,n.key===2]]),J(e("div",null,[e("div",Fl,[(o(!0),h(z,null,q($(ya)(),a=>(o(),f(g,{class:"line_form_item",label:a.unit?`${a.label}(${a.unit})`:a.label,name:a.name,rules:a.rules},{default:d(()=>[a.type==="number"?(o(),f(u,{key:0,class:"input_deal_wrap",defaultValue:a.default,value:t.value[a.name],"onUpdate:value":r=>t.value[a.name]=r,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):a.type==="select"?(o(),f(K,{key:1,size:"small",ref_for:!0,ref:"select",value:t.value[a.name],"onUpdate:value":r=>t.value[a.name]=r,style:{width:"100px"},onChange:s.handleChange},{default:d(()=>[(o(!0),h(z,null,q(a.options,r=>(o(),f(C,{value:r.value},{default:d(()=>[V(P(r.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value","onChange"])):M("",!0)]),_:2},1032,["label","name","rules"]))),256)),c(g,{class:"line_form_item",label:"容量范围(MW)"},{default:d(()=>[e("div",Ol,[c(u,{class:"input_deal_wrap2",value:t.value.es_min_capacity,"onUpdate:value":l[18]||(l[18]=a=>t.value.es_min_capacity=a),size:"small",min:0},null,8,["value"]),Gl,c(u,{class:"input_deal_wrap2",value:t.value.es_max_capacity,"onUpdate:value":l[19]||(l[19]=a=>t.value.es_max_capacity=a),size:"small",min:0},null,8,["value"])])]),_:1})]),e("div",Zl,[Hl,c(ge,{class:"i_val",rowKey:"id","row-selection":{selectedRowKeys:b.selectedBatRowKeys,onChange:Re,type:"radio"},columns:$(ha)(),"data-source":U.value.bat,pagination:!1,size:"small"},null,8,["row-selection","columns","data-source"])])],512),[[Q,n.key===3]]),J(e("div",null,[e("div",Jl,[(o(!0),h(z,null,q($(fa)(),a=>(o(),f(g,{class:"line_form_item",label:a.unit?`${a.label}(${a.unit})`:a.label,name:a.name,rules:a.rules},{default:d(()=>[a.type==="number"?(o(),f(u,{key:0,class:"input_deal_wrap",defaultValue:a.default,value:t.value[a.name],"onUpdate:value":r=>t.value[a.name]=r,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):a.type==="select"?(o(),f(K,{key:1,ref_for:!0,ref:"select",value:t.value[a.name],"onUpdate:value":r=>t.value[a.name]=r,style:{width:"120px"},onChange:s.handleChange},{default:d(()=>[(o(!0),h(z,null,q(a.options,r=>(o(),f(C,{value:r.value},{default:d(()=>[V(P(r.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value","onChange"])):M("",!0)]),_:2},1032,["label","name","rules"]))),256)),c(g,{class:"line_form_item",label:"容量范围(MW)"},{default:d(()=>[e("div",Ql,[c(u,{class:"input_deal_wrap2",value:t.value.ele_min_capacity,"onUpdate:value":l[20]||(l[20]=a=>t.value.ele_min_capacity=a),size:"small",min:0},null,8,["value"]),Xl,c(u,{class:"input_deal_wrap2",value:t.value.ele_max_capacity,"onUpdate:value":l[21]||(l[21]=a=>t.value.ele_max_capacity=a),size:"small",min:0},null,8,["value"])])]),_:1})]),e("div",Yl,[et,c(ge,{class:"i_val",rowKey:"id","row-selection":{selectedRowKeys:b.selectedAlkRowKeys,onChange:ze},columns:$(ga)(),"data-source":U.value.alk,pagination:!1,size:"small"},null,8,["row-selection","columns","data-source"])])],512),[[Q,n.key===4]]),J(e("div",null,[e("div",at,[(o(!0),h(z,null,q($(wa)(),a=>(o(),f(g,{class:"line_form_item",label:a.unit?`${a.label}(${a.unit})`:a.label,name:a.name,rules:a.rules},{default:d(()=>[a.type==="number"?(o(),f(u,{key:0,class:"input_deal_wrap",defaultValue:a.default,value:t.value[a.name],"onUpdate:value":r=>t.value[a.name]=r,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):a.type==="select"?(o(),f(K,{key:1,ref_for:!0,ref:"select",value:t.value[a.name],"onUpdate:value":r=>t.value[a.name]=r,style:{width:"120px"},onChange:s.handleChange},{default:d(()=>[(o(!0),h(z,null,q(a.options,r=>(o(),f(C,{value:r.value},{default:d(()=>[V(P(r.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value","onChange"])):M("",!0)]),_:2},1032,["label","name","rules"]))),256))]),e("div",lt,[e("div",tt,[st,e("div",nt,[c(x,{type:"primary",size:"small",onClick:l[22]||(l[22]=a=>fe.value.open())},{default:d(()=>[V("请选择")]),_:1}),e("div",ot,P(ye.value.desc),1)])])]),e("div",ut,[it,c(ge,{class:"i_val",rowKey:"id","row-selection":{selectedRowKeys:b.selectedAlkStoreRowKeys,onChange:Le,type:"radio"},columns:$(ka)(),"data-source":U.value.alkStorage,pagination:!1,size:"small"},null,8,["row-selection","columns","data-source"])])],512),[[Q,n.key===5]])]),_:2},1032,["tab"]))),128))]),_:1},8,["activeKey"])])])]),e("div",rt,[dt,e("div",ct,[_t,e("div",vt,[(o(!0),h(z,null,q($(xa)(),n=>(o(),f(g,{class:"line_form_item",label:n.unit?`${n.label}(${n.unit})`:n.label,name:n.name,rules:n.rules},{default:d(()=>[n.type==="number"?(o(),f(u,{key:0,class:"input_deal_wrap",defaultValue:n.default,value:t.value[n.name],"onUpdate:value":a=>t.value[n.name]=a,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):M("",!0)]),_:2},1032,["label","name","rules"]))),256))]),mt,e("div",pt,[(o(!0),h(z,null,q($(Ca)(),n=>(o(),f(g,{class:"line_form_item",label:n.unit?`${n.label}(${n.unit})`:n.label,name:n.name,rules:n.rules},{default:d(()=>[n.type==="number"?(o(),f(u,{key:0,class:"input_deal_wrap",defaultValue:n.default,value:t.value[n.name],"onUpdate:value":a=>t.value[n.name]=a,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):M("",!0)]),_:2},1032,["label","name","rules"]))),256))])])])]),_:1},8,["model"])]),_:1},8,["spinning"])])]),e("div",ft,[c(x,{onClick:je,type:"primary",block:"",loading:b.running},{default:d(()=>[V("运行")]),_:1},8,["loading"])]),c(de,{chartList:G.value,config:{modalTitle:"光伏出力曲线",hasZoom:!0,canUploadSelf:!0,default:{id:t.value.pv_forecast_zone_id}},ref_key:"PVPowerModalRef",ref:Y,onSubmit:_e},null,8,["chartList","config"]),c(de,{chartList:ee.value,config:{modalTitle:"光伏设备衰减曲线",hasZoom:!1,canUploadSelf:!1},ref_key:"PVDeviceModalRef",ref:T,onSubmit:ie},null,8,["chartList"]),c(de,{chartList:D.value,config:{modalTitle:"风机出力曲线",hasZoom:!0,canUploadSelf:!0,default:{id:t.value.wind_forecast_zone_id}},ref_key:"WindPowerModalRef",ref:I,onSubmit:B},null,8,["chartList","config"]),c(de,{chartList:be.value,config:{modalTitle:"需氢曲线",hasZoom:!0,canUploadSelf:!1,default:{id:t.value.absorb_id}},ref_key:"H2ConsumeModalRef",ref:fe,onSubmit:De},null,8,["chartList","config"])])}}},Ct=Ce(gt,[["__scopeId","data-v-2c1da801"]]);export{Ct as default};
