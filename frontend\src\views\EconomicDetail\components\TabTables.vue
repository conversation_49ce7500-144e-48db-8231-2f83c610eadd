<template>
  <div class="detail-indicators">
    <div class="section-header">
      <h2 class="section-title">详情指标</h2>
      <a-button
        size="small"
        @click="exportAllTablesToExcel"
        :loading="exportLoading"
      >
        <template #icon>
          <DownloadOutlined />
        </template>
        数据导出
      </a-button>
    </div>

    <a-tabs v-model:activeKey="activeTab" class="detail-tabs">
      <!-- 数据驱动渲染表格 -->
      <a-tab-pane 
        v-for="config in tableConfigs" 
        :key="config.key" 
        :tab="config.tab"
      >
        <a-table
          :columns="getTableColumns(config)"
          :data-source="getTableData(config)"
          :pagination="false"
          size="small"
          bordered
          :scroll="config.type === 'yearly' ? { x: 'max-content' } : undefined"
        />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { DownloadOutlined } from '@ant-design/icons-vue'
import { exportEconomicDetailToExcel } from '@/util'
import {
  tableColumns,
  generateYearColumns,
  tableDataConfigs,
  yearlyTableConfigs,
  generateYearlyTableData,
  generateBasicTableData
} from '../util.js'

// 接收父组件传递的数据
const props = defineProps({
  resultData: {
    type: Object,
    default: () => null
  }
})

const activeTab = ref('2')
const exportLoading = ref(false)

// 统一获取运营年份数量
const operatingYears = computed(() => {
  return props.resultData?.financialIndicatorsSummary?.operatingYears || 25
})

// 年份数量（运营年份+1，包含第0年建设期）
const yearCount = computed(() => {
  return operatingYears.value + 1
})

// 表格配置数组 - 数据驱动渲染
const tableConfigs = [
  {
    key: '2',
    tab: '固定资产投资估算',
    type: 'basic',
    dataKey: 'fixedAssetsInvestmentEstimation',
    configKey: 'fixedAssets'
  },
  {
    key: '3',
    tab: '工程总概算',
    type: 'basic',
    dataKey: 'projectOverallBudget',
    configKey: 'projectBudget'
  },
  {
    key: '4',
    tab: '融资计划',
    type: 'basic',
    dataKey: 'financingPlan',
    configKey: 'financing'
  },
  {
    key: '5',
    tab: '投资计划与资金筹措',
    type: 'basic',
    dataKey: 'investmentPlanAndFundRaising',
    configKey: 'investmentPlan'
  },
  {
    key: '6',
    tab: '年度制氢及上网电量',
    type: 'yearly',
    dataKey: 'annualHydrogenAndGridPower',
    configKey: 'annualHydrogenAndGridPower'
  },
  {
    key: '7',
    tab: '还本付息计算',
    type: 'yearly',
    dataKey: 'loanRepaymentSchedule',
    configKey: 'loanRepaymentSchedule'
  },
  {
    key: '8',
    tab: '总成本费用',
    type: 'yearly',
    dataKey: 'totalCostAndExpenses',
    configKey: 'totalCostAndExpenses'
  },
  {
    key: '9',
    tab: '利润和利润分配',
    type: 'yearly',
    dataKey: 'profitAndProfitDistribution',
    configKey: 'profitAndProfitDistribution'
  },
  {
    key: '10',
    tab: '项目投资现金流量',
    type: 'yearly',
    dataKey: 'projectInvestmentCashFlow',
    configKey: 'projectInvestmentCashFlow'
  },
  {
    key: '11',
    tab: '资本金财务现金流量',
    type: 'yearly',
    dataKey: 'equityCapitalCashFlow',
    configKey: 'equityCapitalCashFlow'
  },
  {
    key: '12',
    tab: '财务计划现金流量',
    type: 'yearly',
    dataKey: 'financialPlanCashFlow',
    configKey: 'financialPlanCashFlow'
  },
  {
    key: '13',
    tab: '资产负债',
    type: 'yearly',
    dataKey: 'balanceSheet',
    configKey: 'balanceSheet'
  },
  {
    key: '14',
    tab: '财务指标汇总',
    type: 'basic',
    dataKey: 'financialIndicatorsSummary',
    configKey: 'financialSummary'
  }
]

// 动态生成表格数据
const getTableColumns = (config) => {
  if (config.type === 'yearly') {
    return generateYearColumns(yearCount.value)
  } else {
    return tableColumns[config.configKey]
  }
}

const getTableData = (config) => {
  if (!props.resultData?.[config.dataKey]) return []

  if (config.type === 'yearly') {
    return generateYearlyTableData(
      props.resultData[config.dataKey],
      yearlyTableConfigs[config.configKey],
      yearCount.value
    )
  } else {
    return generateBasicTableData(
      props.resultData[config.dataKey],
      tableDataConfigs[config.configKey]
    )
  }
}

// 导出所有表格到Excel
const exportAllTablesToExcel = async () => {
  if (!props.resultData) {
    message.warning('暂无数据可导出')
    return
  }

  try {
    exportLoading.value = true

    // 使用工具函数导出
    exportEconomicDetailToExcel(
      tableConfigs,
      props.resultData,
      getTableColumns,
      getTableData,
      '经济分析详情数据.xlsx'
    )

    message.success('Excel文件导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  } finally {
    exportLoading.value = false
  }
}
</script>

<style scoped>
.detail-indicators {
  background: white;
  padding: 16px;
  border-radius: 4px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 700;
  margin: 0;
  color: #333;
}

.detail-tabs {
  margin-top: 16px;
}

:deep(.ant-tabs-card .ant-tabs-tab) {
  background: #fafafa;
  border-color: #d9d9d9;
  font-size: 12px;
  padding: 8px 12px;
}

:deep(.ant-tabs-card .ant-tabs-tab-active) {
  background: white;
  border-bottom-color: white;
}

:deep(.ant-table) {
  font-size: 12px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 500;
  color: #333;
  padding: 8px;
  font-size: 12px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

:deep(.ant-tabs-content-holder) {
  padding-top: 16px;
}
</style> 