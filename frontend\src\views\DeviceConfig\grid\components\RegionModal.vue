<template>
  <a-modal
    :visible="visible"
    title="新增地区"
    :width="500"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="地区类型" name="regionType">
        <a-radio-group v-model:value="formData.regionType" @change="handleRegionTypeChange">
          <a-radio :value="'existing'">选择已有地区</a-radio>
          <a-radio :value="'custom'">自定义地区</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 已有地区选择 -->
      <template v-if="formData.regionType === 'existing'">
        <a-form-item label="地区选择" name="province">
          <a-cascader
            v-model:value="formData.cascaderValue"
            :options="cascaderOptions"
            placeholder="请选择省份/城市"
            :show-search="true"
            @change="handleCascaderChange"
          />
        </a-form-item>
      </template>

      <!-- 自定义地区 -->
      <template v-else>
        <a-form-item label="地区名称" name="customRegion">
          <a-input 
            v-model:value="formData.customRegion"
            placeholder="请输入自定义地区名称"
          />
        </a-form-item>
      </template>

      <a-form-item label="电价区间" name="period">
        <a-select v-model:value="formData.period" placeholder="请选择电价区间">
          <a-select-option value="year">年度(整年)</a-select-option>
          <a-select-option value="halfYear">半年(上下半年)</a-select-option>
          <a-select-option value="quarter">季度(四个季度)</a-select-option>
          <a-select-option value="month">月度(12个月)</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { getZone } from '@/api/project'

const props = defineProps({
  visible: Boolean,
  existingRegions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'confirm'])
const regionData = ref({})
const formRef = ref()
const formData = reactive({
  regionType: 'existing',
  province: '',
  city: '',
  customRegion: '',
  period: 'quarter',
  cascaderValue: [],
  selectedZoneInfo: null
})

const rules = {
  regionType: [{ required: true, message: '请选择地区类型' }],
  province: [{ required: true, message: '请选择省份' }],
  city: [{ required: true, message: '请选择城市' }],
  customRegion: [{ required: true, message: '请输入地区名称' }],
  period: [{ required: true, message: '请选择电价区间' }]
}

// 构建cascader选项
const cascaderOptions = computed(() => {
  return regionData.value.result.map(province => ({
    value: province.province,
    label: province.province,
    children: province.subZones.map(city => ({
      value: city.city,
      label: city.city,
      zoneId: city.id,
      region: city.region,
      country: city.country
    }))
  }))
})

const handleRegionTypeChange = () => {
  formData.province = ''
  formData.city = ''
  formData.customRegion = ''
  formData.cascaderValue = []
  formData.selectedZoneInfo = null
}

const handleCascaderChange = (value, selectedOptions) => {
  if (value && value.length === 2 && selectedOptions && selectedOptions.length === 2) {
    formData.province = value[0]
    formData.city = value[1]
    // 存储选中的地区信息
    formData.selectedZoneInfo = {
      zoneId: selectedOptions[1].zoneId,
      region: selectedOptions[1].region,
      country: selectedOptions[1].country
    }
  } else {
    formData.province = ''
    formData.city = ''
    formData.selectedZoneInfo = null
  }
}

const handleConfirm = async () => {
  try {
    await formRef.value.validate()

    let regionData = {
      period: formData.period
    }

    if (formData.regionType === 'existing') {
      // 检查是否已存在相同的省市组合
      const isDuplicate = props.existingRegions.some(region =>
        region.province === formData.province && region.city === formData.city
      )

      if (isDuplicate) {
        message.error('该地区已存在，不能重复添加')
        return
      }

      regionData = {
        ...regionData,
        zoneId: formData.selectedZoneInfo?.zoneId || Date.now(),
        region: formData.selectedZoneInfo?.region || '',
        country: formData.selectedZoneInfo?.country || '中国',
        province: formData.province,
        city: formData.city,
        groupedTouPriceByMonth: [] // 保持兼容性，组件内部仍使用此结构
      }
    } else {
      // 检查自定义地区是否已存在
      const isDuplicate = props.existingRegions.some(region =>
        region.province === formData.customRegion && !region.city
      )

      if (isDuplicate) {
        message.error('该自定义地区已存在，不能重复添加')
        return
      }

      regionData = {
        ...regionData,
        zoneId: null, // 自定义地区ID为空
        region: '自定义地区',
        country: '中国',
        province: formData.customRegion,
        city: '',
        groupedTouPriceByMonth: [] // 保持兼容性，组件内部仍使用此结构
      }
    }

    emit('confirm', regionData)
    handleCancel()
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

const handleCancel = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
  Object.assign(formData, {
    regionType: 'existing',
    province: '',
    city: '',
    customRegion: '',
    period: 'quarter',
    cascaderValue: [],
    selectedZoneInfo: null
  })
}

const getZoneData = async() => {
  const { code, msg, data } = await getZone()
  if (code === 0) {
    regionData.value = data
  } else {
    message.error(msg)
  }
}

// 监听visible变化，重置表单
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    handleCancel()
  }
})

onMounted(async () => {
  getZoneData()
})
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}
</style>



