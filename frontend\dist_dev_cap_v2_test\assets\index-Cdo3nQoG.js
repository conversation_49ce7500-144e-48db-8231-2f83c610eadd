import{k as se,g as le}from"./index-BcHJMLm2.js";import{_ as ne,u as re,d as ie,r as p,o as ce,f as v,b as m,c as j,g as s,t as r,h as i,w as c,i as b,A as E,n as w,p as h,m as C,F as M,l as O,D as ue,E as de}from"./index-k3sxFAFL.js";import{p as H,i as pe,c as me,t as ve,a as _e}from"./util-CfYhg8wl.js";import{l as fe}from"./lodash-B2Sw4o0r.js";/* empty css                                                              */import{f as be,g as K}from"./index-CB608jyz.js";const ge=L=>(ue("data-v-c824ec83"),L=L(),de(),L),he={class:"body_wrap"},je={class:"p_wrap"},ke=ge(()=>s("div",{class:"title_wrap"},null,-1)),ye={class:"content_wrap",id:"content_wrap"},we={class:"part_wrap"},Le={class:"p_title"},$e={class:"btn_wrap"},Pe={class:"table_wrap"},Ce={key:0,class:"t_btn_wrap"},Se=["onClick"],Ne=["onClick"],Re={key:0,class:"t_btn_wrap"},Ie=["onClick"],qe=["onClick"],Me=["onClick"],ze={__name:"index",setup(L){const{t:G}=re(),$=ie(),S=p(!1),P=p(!1),z=p(),A=p([]),_=p({}),T=p({}),N=p(!1),D=p(!1),B=p(0),n=p({operator:"",pageSize:10,pageNumber:1,searchWord:""}),J=e=>{const t=[],l=ve();for(let a=0,u=l.length;a<u;a++)(e==null?void 0:e[a])!==0&&t.push({label:l[a],value:e==null?void 0:e[a]});return t},Q=e=>{const t=[],l=_e();for(let a=0,u=l.length;a<u;a++)e!=null&&e[a]&&t.push({label:l[a]});return t},X=()=>{$.push({name:"createProject"})},U=(e,t)=>{console.log("ceateSolution:",e,t);const l={projectId:e.id};(t==null?void 0:t.id)!==void 0&&(l.solutionId=t.id),$.push({name:"createProject",query:l})},Y=e=>{P.value=!0,T.value=e,_.value=fe.cloneDeep(e)},Z=async()=>{const e=await z.value.validateFields();N.value=!0;const{code:t,msg:l}=await se({id:T.value.id,...e});N.value=!1,t===0?(E.success(G("projectList.editProjectModal.editSuccess")),V()):E.error(l),P.value=!1},x=(e,t)=>{$.push({name:"economicAnalysisCreate",query:{capProjectId:e.id,capSolutionId:t.id}})},R=async(e=!0)=>{var k;S.value=e;const t={pageSize:n.value.pageSize,pageNumber:n.value.pageNumber};n.value.searchWord&&(t.search=n.value.searchWord),(k=n.value.operator)!=null&&k.trim()&&(t.operator=n.value.operator);const{msg:l,data:a,code:u}=await le(t);console.log("code:",u,a),S.value=!1,u===0&&(A.value=a.result,B.value=a.total)},ee=e=>{console.log("page:",e),n.value.pageNumber=e.current,n.value.pageSize=e.pageSize,R()},V=async()=>{R()};return ce(()=>{V(),setTimeout(()=>{D.value=!0},5e3)}),(e,t)=>{const l=v("a-button"),a=v("a-input-search"),u=v("a-tag"),k=v("a-table"),W=v("a-input"),I=v("a-form-item"),te=v("a-textarea"),ae=v("a-form"),oe=v("a-modal");return m(),j("div",he,[s("div",je,[ke,s("div",ye,[s("div",we,[s("div",Le,[s("div",null,r(e.$t("projectList.title")),1),s("div",$e,[i(l,{class:"btn_item",size2:"small",type:"primary",onClick:X},{default:c(()=>[w(r(e.$t("projectList.buttons.newProject")),1)]),_:1})])]),s("div",Pe,[s("div",null,[i(a,{value:n.value.searchWord,"onUpdate:value":t[0]||(t[0]=o=>n.value.searchWord=o),placeholder:e.$t("projectList.search.placeholder"),style:{width:"230px","margin-bottom":"15px"},onSearch:R},null,8,["value","placeholder"])]),i(k,{borderd:"",class:"outer_table ant-table-striped","row-class-name":(o,f)=>f%2===1?"table-striped":null,loading:S.value,columns:b(me)(),rowKey:"id","data-source":A.value,defaultExpandAllRows:D.value,onChange:ee,pagination:{pageSize:n.value.pageSize,total:B.value,hideOnSinglePage:!1,showTotal:o=>e.$t("projectList.pagination.total",{total:o})}},{bodyCell:c(({column:o,record:f})=>[o.key==="action"?(m(),j("div",Ce,[s("a",{href:"void:0",class:"a_item",onClick:g=>U(f)},r(e.$t("projectList.buttons.newSolution")),9,Se),s("a",{href:"void:0",class:"a_item",onClick:g=>Y(f)},r(e.$t("projectList.buttons.editProject")),9,Ne)])):h("",!0),o.key==="solution"?(m(),C(k,{key:1,class:"inner_table",showHeader:!0,columns:b(pe)(),"data-source":f.solutions,pagination:!1,size:"small",defaultExpandAllRows:!0},{bodyCell:c(({column:g,text:y,record:q})=>{var F;return[g.key==="action"?(m(),j("div",Re,[s("a",{class:"a_item",onClick:d=>b($).push({name:"projectDetail",query:{projectId:f.id,solutionId:q.id,type:"list"}})},r(e.$t("projectList.buttons.view")),9,Ie),s("a",{href:"void:0",class:"a_item",onClick:d=>U(f,q)},r(e.$t("projectList.buttons.editSolution")),9,qe),s("a",{href:"void:0",class:"a_item",onClick:d=>x(f,q)},r(e.$t("projectList.buttons.economicAnalysis")),9,Me)])):h("",!0),g.key==="createTime"?(m(),j(M,{key:1},[w(r(b(be)(y)),1)],64)):h("",!0),g.key==="status"?(m(),C(u,{key:2,color:(F=b(K)(b(H)(),y))==null?void 0:F.color},{default:c(()=>{var d;return[w(r((d=b(K)(b(H)(),y))==null?void 0:d.label),1)]}),_:2},1032,["color"])):h("",!0),g.key==="topology"?(m(!0),j(M,{key:3},O(Q(y),d=>(m(),C(u,null,{default:c(()=>[w(r(d.label),1)]),_:2},1024))),256)):h("",!0),g.key==="targetExpr"?(m(!0),j(M,{key:4},O(J(y),d=>(m(),C(u,null,{default:c(()=>[w(r(`${d.label}: ${d.value*100}%`),1)]),_:2},1024))),256)):h("",!0)]}),_:2},1032,["columns","data-source"])):h("",!0)]),_:1},8,["row-class-name","loading","columns","data-source","defaultExpandAllRows","pagination"])])])])]),i(oe,{open:P.value,"onUpdate:open":t[4]||(t[4]=o=>P.value=o),title:e.$t("projectList.editProjectModal.title"),onOk:Z,"confirm-loading":N.value},{default:c(()=>[s("div",null,[i(ae,{labelAlign:"left2",ref_key:"formRef",ref:z,model:_.value,name:"basic","label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:c(()=>[i(I,{label:e.$t("projectList.editProjectModal.projectName"),name:"name",rules:[{required:!0,message:e.$t("projectList.editProjectModal.projectNameRequired")}]},{default:c(()=>[i(W,{value:_.value.name,"onUpdate:value":t[1]||(t[1]=o=>_.value.name=o)},null,8,["value"])]),_:1},8,["label","rules"]),i(I,{label:e.$t("projectList.editProjectModal.customerName"),name:"customer",rules:[{required:!0,message:e.$t("projectList.editProjectModal.customerNameRequired")}]},{default:c(()=>[i(W,{value:_.value.customer,"onUpdate:value":t[2]||(t[2]=o=>_.value.customer=o)},null,8,["value"])]),_:1},8,["label","rules"]),i(I,{label:e.$t("projectList.editProjectModal.projectBackground"),name:"desc",rules:[{required:!0,message:e.$t("projectList.editProjectModal.projectBackgroundRequired")}]},{default:c(()=>[i(te,{value:_.value.desc,"onUpdate:value":t[3]||(t[3]=o=>_.value.desc=o)},null,8,["value"])]),_:1},8,["label","rules"])]),_:1},8,["model"])])]),_:1},8,["open","title","confirm-loading"])])}}},We=ne(ze,[["__scopeId","data-v-c824ec83"]]);export{We as default};
