import{u as ie,l as le,d as se,n as re}from"./index-BcHJMLm2.js";import{_ as ce,r as p,x as Se,e as Ee,G as Ue,f as c,b as i,m as v,w as o,h as f,n as w,A as P,K as Ke,o as ue,z as Ae,c as g,g as l,i as C,P as Ne,t as Z,J as Ze,p as b,F as V,l as G,y as Ve,D as de,E as pe,M as L,d as _e}from"./index-k3sxFAFL.js";import{i as Le,l as H}from"./lodash-B2Sw4o0r.js";/* empty css                                                              */import{g as O}from"./index-CB608jyz.js";const ve=()=>[{title:"",dataIndex:""},{title:"操作",dataIndex:"action",key:"action"}],fe=()=>[{title:"",dataIndex:""},{title:"操作",dataIndex:"action",key:"action"}],E=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:""},{title:"单机功率(MW)",dataIndex:"single_power",s_type:"number",rules:[{required:!1}],unit:""},{title:"充电效率",dataIndex:"charge_efficiency",s_type:"number",rules:[{required:!1}],unit:""},{title:"放电效率",dataIndex:"discharge_efficiency",s_type:"number",rules:[{required:!1}],unit:""},{title:"充放电倍率",dataIndex:"c_rate",s_type:"number",rules:[{required:!1}],unit:""},{title:"初始SOC",dataIndex:"init_soc",s_type:"number",rules:[{required:!1}],unit:""},{title:"SOC下限",dataIndex:"min_soc",s_type:"number",rules:[{required:!1}],unit:""},{title:"SOC上限",dataIndex:"max_soc",s_type:"number",rules:[{required:!1}],unit:""},{title:"寿命(年)",dataIndex:"life_cycle",s_type:"number",rules:[{required:!1}],unit:""},{title:"操作",dataIndex:"action",key:"action"}],F=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:"",width:"80px"},{title:"类型",dataIndex:"ele_type",key:"ele_type",s_type:"select",rules:[{required:!1}],unit:"",options:[{label:"ALK",value:1,color:"blue"},{label:"PEM",value:2,color:"green"}]},{title:"容量(Nm³/h)",dataIndex:"capacity",s_type:"number",rules:[{required:!1}],unit:""},{title:"能耗(kwh/Nm³)",dataIndex:"power_consumption",s_type:"number",rules:[{required:!1}],unit:""},{title:"额定功率(MW)",dataIndex:"pe",s_type:"number",rules:[{required:!1}],unit:""},{title:"最低负载率",dataIndex:"lower_load_rate",s_type:"number",rules:[{required:!1}],unit:""},{title:"最高负载率",dataIndex:"upper_load_rate",s_type:"number",rules:[{required:!1}],unit:""},{title:"辅助系统能耗(kwh/Nm³)",dataIndex:"assist_consumption",s_type:"number",rules:[{required:!1}],unit:""},{title:"系统价格(元/套)",dataIndex:"price",s_type:"number",rules:[{required:!1}],unit:""},{title:"制氢电源效率",dataIndex:"power_supply_efficiency",s_type:"number",rules:[{required:!1}],unit:""},{title:"操作",dataIndex:"action",key:"action",width:"100px"}],W=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:""},{title:"体积(m³)",dataIndex:"volume",s_type:"number",rules:[{required:!1}],unit:""},{title:"最小运行压力(Mpa)",dataIndex:"min_pressure",s_type:"number",rules:[{required:!1}],unit:""},{title:"最大运行压力(Mpa)",dataIndex:"max_pressure",s_type:"number",rules:[{required:!1}],unit:""},{title:"价格(元)",dataIndex:"price",s_type:"number",rules:[{required:!1}],unit:""},{title:"占地面积(㎡)",dataIndex:"area",s_type:"number",rules:[{required:!1}],unit:""},{title:"操作",dataIndex:"action",key:"action"}];const j=$=>(de("data-v-3c825eb6"),$=$(),pe(),$),me={class:"body_wrap"},ye={class:"p_wrap"},ge=j(()=>l("div",{class:"title_wrap"},null,-1)),be={class:"content_wrap",id:"content_wrap"},he={class:"part_wrap"},xe=j(()=>l("div",{class:"p_title"},[l("div",null,"设备配置"),l("div",{class:"btn_wrap"})],-1)),ke={class:"tab_wrap"},Ie={key:0,class:"t_btn_wrap"},we=["onClick"],Ce=["onClick"],$e={key:0,class:"t_btn_wrap"},De=["onClick"],Te=["onClick"],qe={key:0,class:"t_btn_wrap"},Re=["onClick"],Pe=["onClick"],ze={__name:"index",setup($){L.useModal(),_e();const h=p(!1),x=p(!1),U=p(),k=p([]),u=p({}),K=p({}),z=p(!1),I=p(3);p([]);const D=p(!1),T=p(E().filter(n=>n.s_type)),A=n=>[ve,fe,function(){return[]},E,F,W][n]().filter(s=>s.s_type),M=(n,t)=>{L.confirm({title:"确认删除?",async onOk(){const{code:s,msg:d}=await re({devIdList:[n.id]});s===0?(P.success("删除成功"),q()):P.error(d)}})},B=(n,t)=>{x.value=!0,D.value=!0,T.value=A(t),K.value=n,u.value=H.cloneDeep(n)},S=n=>{x.value=!0,D.value=!1,T.value=A(n),console.log("form item:",T.value,n)},Y=n=>{I.value=n,q()},J=async()=>{var m;await U.value.validateFields();const n=I.value,t={manufacturer:u.value.manufacturer,model:u.value.model};let s,d;z.value=!0;const r=H.cloneDeep(u.value);if(delete r.manufacturer,delete r.model,delete r.id,delete r.category,D.value){t.id=(m=K.value)==null?void 0:m.id;const _=await ie([{type:n,baseInfo:t,params:r}]);s=_.code,d=_.msg}else{const _=await le([{type:n,baseInfo:t,params:r}]);s=_.code,d=_.msg}z.value=!1,s===0?q():P.error(d),x.value=!1},q=async n=>{h.value=!0;const{code:t,data:s,msg:d}=await se({type:I.value});t===0?(k.value=s.result.map(r=>{const{baseInfo:m,params:_}=r;return{...m,..._}}),console.log("table:",k.value)):P.error(d),h.value=!1};return ue(()=>{q()}),(n,t)=>{const s=c("a-button"),d=c("a-table"),r=c("a-tab-pane"),m=c("a-tag"),_=c("a-tabs"),Q=c("a-input-number"),X=c("a-input"),ee=c("a-select-option"),te=c("a-select"),ne=c("a-form-item"),ae=c("a-form"),oe=c("a-modal");return i(),g("div",me,[l("div",ye,[ge,l("div",be,[l("div",he,[xe,l("div",ke,[f(_,{activeKey:I.value,"onUpdate:activeKey":t[3]||(t[3]=e=>I.value=e),onChange:Y},{default:o(()=>[(i(),v(r,{key:3,tab:"储能"},{default:o(()=>[f(s,{style:{margin:"-10px 0 10px 0"},size:"small",onClick:t[0]||(t[0]=e=>S(3))},{default:o(()=>[w("新增")]),_:1}),f(d,{size:"small",loading:h.value,pagination:!1,columns:C(E)(),rowKey:"id","data-source":k.value,defaultExpandAllRows:!0},{bodyCell:o(({column:e,record:a})=>[e.key==="action"?(i(),g("div",Ie,[l("a",{class:"a_item",onClick:y=>B(a,3)},"修改",8,we),l("a",{class:"a_item",onClick:y=>M(a,3)},"删除",8,Ce)])):b("",!0)]),_:1},8,["loading","columns","data-source"])]),_:1})),(i(),v(r,{key:4,tab:"电解槽"},{default:o(()=>[f(s,{style:{margin:"-10px 0 10px 0"},size:"small",onClick:t[1]||(t[1]=e=>S(4))},{default:o(()=>[w("新增")]),_:1}),f(d,{size:"small",loading:h.value,pagination:!1,columns:C(F)(),rowKey:"id","data-source":k.value,defaultExpandAllRows:!0},{bodyCell:o(({column:e,record:a,text:y})=>{var N;return[e.key==="action"?(i(),g("div",$e,[l("a",{href:"void:0",class:"a_item",onClick:R=>B(a,4)},"修改",8,De),l("a",{href:"void:0",class:"a_item",onClick:R=>M(a,4)},"删除",8,Te)])):b("",!0),e.key==="ele_type"?(i(),v(m,{key:1,color:(N=C(O)(e.options,y))==null?void 0:N.color},{default:o(()=>{var R;return[w(Z((R=C(O)(e.options,y))==null?void 0:R.label),1)]}),_:2},1032,["color"])):b("",!0)]}),_:1},8,["loading","columns","data-source"])]),_:1})),(i(),v(r,{key:5,tab:"储罐"},{default:o(()=>[f(s,{style:{margin:"-10px 0 10px 0"},size:"small",onClick:t[2]||(t[2]=e=>S(5))},{default:o(()=>[w("新增")]),_:1}),f(d,{size:"small",loading:h.value,pagination:!1,columns:C(W)(),rowKey:"id","data-source":k.value,defaultExpandAllRows:!0},{bodyCell:o(({column:e,record:a})=>[e.key==="action"?(i(),g("div",qe,[l("a",{href:"void:0",class:"a_item",onClick:y=>B(a,5)},"修改",8,Re),l("a",{href:"void:0",class:"a_item",onClick:y=>M(a,5)},"删除",8,Pe)])):b("",!0)]),_:1},8,["loading","columns","data-source"])]),_:1})),b("",!0)]),_:1},8,["activeKey"])])])])]),f(oe,{open:x.value,"onUpdate:open":t[4]||(t[4]=e=>x.value=e),title:D.value?"修改设备":"创建设备",onOk:J,"confirm-loading":z.value,destroyOnClose:""},{default:o(()=>[l("div",null,[f(ae,{labelAlign:"left2",ref_key:"formRef",ref:U,model:u.value,name:"basic","label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:o(()=>[(i(!0),g(V,null,G(T.value,e=>(i(),v(ne,{label:e.title,name:e.dataIndex,rules:e.rules},{default:o(()=>[e.s_type==="number"?(i(),v(Q,{key:0,style:{width:"100%"},value:u.value[e.dataIndex],"onUpdate:value":a=>u.value[e.dataIndex]=a,size:"small",min:0},null,8,["value","onUpdate:value"])):e.s_type==="string"?(i(),v(X,{key:1,value:u.value[e.dataIndex],"onUpdate:value":a=>u.value[e.dataIndex]=a},null,8,["value","onUpdate:value"])):e.s_type==="select"?(i(),v(te,{key:2,value:u.value[e.dataIndex],"onUpdate:value":a=>u.value[e.dataIndex]=a,style2:"width: 120px"},{default:o(()=>[(i(!0),g(V,null,G(e.options,a=>(i(),v(ee,{value:a.value},{default:o(()=>[w(Z(a.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value"])):b("",!0)]),_:2},1032,["label","name","rules"]))),256))]),_:1},8,["model"])])]),_:1},8,["open","title","confirm-loading"])])}}},Fe=ce(ze,[["__scopeId","data-v-3c825eb6"]]);export{Fe as default};
