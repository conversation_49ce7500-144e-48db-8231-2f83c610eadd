import{I as w,r as b,u as g,x as h,d as s,b as x,c as y,e as d,f as e,w as o,j as I,y as S,p as B,m as C,_ as N}from"./index-D4CjQwBG.js";const V=t=>(B("data-v-33a80934"),t=t(),C(),t),k={class:"bg_wrap"},q={class:"login_window"},U=V(()=>d("div",{class:"title"},"管理系统",-1)),j=w({__name:"index",setup(t){const r=b(!1),u=g(),a=h({name:"",password:""}),m=async _=>{r.value=!0,r.value=!1,code===0?u.push({name:"home"}):S.error(msg)};return(_,n)=>{const c=s("a-input"),l=s("a-form-item"),i=s("a-input-password"),f=s("a-button"),v=s("a-form");return x(),y("div",k,[d("div",q,[U,e(v,{class:"form_wrap",model:a,name:"basic","label-col":{span:6},"wrapper-col":{span:14},autocomplete:"off",onFinish:m},{default:o(()=>[e(l,{label:"用户名",name:"name",rules:[{required:!0,message:"请输入用户名!"}]},{default:o(()=>[e(c,{value:a.name,"onUpdate:value":n[0]||(n[0]=p=>a.name=p)},null,8,["value"])]),_:1}),e(l,{label:"密码",name:"password",rules:[{required:!0,message:"请输入密码!"}]},{default:o(()=>[e(i,{value:a.password,"onUpdate:value":n[1]||(n[1]=p=>a.password=p)},null,8,["value"])]),_:1}),e(l,{"wrapper-col":{offset:6,span:14}},{default:o(()=>[e(f,{loading:r.value,style:{width:"100%"},type:"primary","html-type":"submit"},{default:o(()=>[I(" 登录 ")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])])])}}}),F=N(j,[["__scopeId","data-v-33a80934"]]);export{F as default};
