export default {
  // Page title
  title: 'Capacity Calculation',
  
  // Search
  search: {
    placeholder: 'Enter project name or customer name'
  },
  
  // Buttons
  buttons: {
    newProject: 'New Project',
    newSolution: 'New Solution',
    editProject: 'Edit Project',
    view: 'View',
    editSolution: 'Edit Solution',
    economicAnalysis: 'Economic Analysis'
  },
  
  // Table columns
  columns: {
    // Outer table
    projectName: 'Project Name',
    customerName: 'Customer Name',
    projectBackground: 'Project Background',
    projectAction: 'Project Action',
    solution: 'Solution',
    
    // Inner table
    solutionName: 'Name',
    scenario: 'Scenario',
    solveTarget: 'Solve Target',
    createTime: 'Create Time',
    status: 'Status'
  },
  
  // Pagination
  pagination: {
    total: 'Total {total} pages'
  },
  
  // Project status
  status: {
    running: 'Running',
    success: 'Success',
    failed: 'Failed'
  },
  
  // Solve targets
  targets: {
    lcoh: 'LCOH',
    lowestInvestment: 'Lowest Investment Cost',
    lowestAbandonRate: 'Lowest Abandon Rate',
    maxHydrogenProduction: 'Max Hydrogen Production'
  },
  
  // Scenario/Topology
  topology: {
    photovoltaic: 'Photovoltaic',
    windTurbine: 'Wind Turbine',
    grid: 'Grid',
    energyStorage: 'Energy Storage',
    hydrogenProduction: 'Hydrogen Production',
    hydrogenStorage: 'Hydrogen Storage'
  },
  
  // Edit project modal
  editProjectModal: {
    title: 'Edit Project',
    projectName: 'Project Name',
    customerName: 'Customer Name',
    projectBackground: 'Project Background',
    projectNameRequired: 'Please enter project name',
    customerNameRequired: 'Please enter customer name',
    projectBackgroundRequired: 'Please enter project background',
    editSuccess: 'Edit successful'
  }
}
