// 基本信息表单配置
export const formBasicInfo = () => [
  {
    label: '项目名称',
    name: 'projectName',
    unit: '',
    type: 'project-select',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '客户名称',
    name: 'customerName',
    unit: '',
    type: 'string',
    rules: [{ required: false, message: '请输入' }],
    visible: true
  },
  {
    label: '项目运营期',
    name: 'operatingYears',
    unit: '年',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '贷款年限',
    name: 'loanTerm',
    unit: '年',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '项目场景',
    name: 'projectScene',
    unit: '',
    type: 'checkbox-group',
    rules: [{ required: true, message: '请至少选择一项' }],
    default: [1, 1, 0, 1, 1, 0], // [光伏,风电,电网,储能,制氢,储氢]
    visible: true,
    options: [
      { label: '光伏', value: 'pv', index: 0 },
      { label: '风电', value: 'wind', index: 1 },
      { label: '电网', value: 'grid', index: 2 },
      { label: '储能', value: 'storage', index: 3 },
      { label: '制氢', value: 'hydrogen', index: 4 },
      { label: '储氢', value: 'hydrogenStorage', index: 5 }
    ]
  },
  {
    label: '项目描述',
    name: 'desc',
    unit: '',
    type: 'textarea',
    rules: [{ required: false, message: '请输入' }],
    visible: true
  },
  {
    label: 'test title',
    name: 'testTitle',
    unit: '',
    type: 'string',
    rules: [{ required: false, message: '请输入' }],
    visible: false
  }
]

// 项目场景与模块映射关系 - 已废弃，使用新的checkbox格式
// export const projectSceneMapping = {
//   0: ['wind', 'hydrogen'], // 风机、制氢
//   1: ['pv', 'hydrogen'], // 光伏、制氢
//   2: ['wind', 'pv', 'hydrogen'], // 风机、光伏、制氢
//   3: ['wind', 'grid', 'hydrogen'], // 风机、电网、制氢
//   4: ['pv', 'grid', 'hydrogen'], // 光伏、电网、制氢
//   5: ['wind', 'pv', 'grid', 'hydrogen'], // 风机、光伏、电网、制氢
//   6: ['wind', 'hydrogen', 'hydrogenStorage'], // 风机、制氢、储氢
//   7: ['pv', 'hydrogen', 'hydrogenStorage'], // 光伏、制氢、储氢
//   8: ['wind', 'pv', 'hydrogen', 'hydrogenStorage'], // 风机、光伏、制氢、储氢
//   9: ['wind', 'grid', 'hydrogen', 'hydrogenStorage'], // 风机、电网、制氢、储氢
//   10: ['pv', 'grid', 'hydrogen', 'hydrogenStorage'], // 光伏、电网、制氢、储氢
//   11: ['wind', 'pv', 'grid', 'hydrogen', 'hydrogenStorage'] // 风机、光伏、电网、制氢、储氢
// }

// 光伏配置
const pvInvestmentConfig = [
  {
    label: '容量',
    name: 'pvCapacity',
    unit: 'MW',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '设备购置费',
    name: 'photovoltaicEquipment',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '安装工程费',
    name: 'photovoltaicInstallation',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '建筑工程费',
    name: 'photovoltaicBuilding',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '其他费用',
    name: 'photovoltaicOthers',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  }
]

const pvCostConfig = [
  {
    label: '材料费计提率',
    name: 'photovoltaicMaterialsCostProvisionRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '光伏首年维修费率',
    name: 'photovoltaicMaintCostRateYear2',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '光伏保险费率',
    name: 'photovoltaicInsuranceRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '光伏其他费用单价',
    name: 'photovoltaicOtherCostUnitPrice',
    unit: '元/kw',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  }
]

const pvProfitConfig = [
  {
    label: '首年发电小时数(h)',
    name: 'pvFirstYearPowerGenerationHour',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true,
    // notShowFromCapacity: true
  },
  {
    label: '运维首年衰减率',
    name: 'yearTwoDegradationPercentage',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '衰减率增长率',
    name: 'yearNDegradationPercentage',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
]

// 辅助计算配置
const pvInvestmentAuxConfig = [
  {
    label: '光伏EPC',
    name: 'pvEPC',
    unit: '元/W',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    visible: true
  },
  {
    label: '设备费占比',
    name: 'pvEquipmentRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '安装费占比',
    name: 'pvInstallRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '建筑费占比',
    name: 'pvBuildingRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '其他费占比',
    name: 'pvOthersRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 扩展参数配置
const pvCostExtendConfig = [
  {
    label: '光伏折旧年限',
    name: 'photovoltaicDepreciationYears',
    unit: '年',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '光伏大修更换费率',
    name: 'photovoltaicMajorOverhaulReplacementRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '光伏材料费年均增长率',
    name: 'photovoltaicMaterialsCostAnnualGrowthRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '光伏第7到11年维修费年增长率',
    name: 'photovoltaicMaintCostGrowthRateYears7to11',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '光伏第12到N年维修费年增长率',
    name: 'photovoltaicMaintCostGrowthRateYears12plus',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '光伏其他费用年增长率',
    name: 'photovoltaicOtherCostGrowthRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

const pvProfitExtendConfig = [
  {
    label: '发电率',
    name: 'test_fadian',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 运营参数配置
export const formOperationConfig = () => [
  {
    label: '运营期氢气售价',
    name: 'hydrogenPriceWithTax',
    unit: '元/kg',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '氢气售价不含税',
    name: 'hydrogenPriceNoTax',
    unit: '元/kg',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '氧气售价不含税',
    name: 'oxygenPriceNoTax',
    unit: '元/kg',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  // {
  //   label: '氧气售价含税',
  //   name: 'oxygenPriceWithTax',
  //   unit: '元/kg',
  //   type: 'number',
  //   rules: [{ required: true, message: '请输入' }],
  //   visible: true
  // },
  // {
  //   label: '上网电价含增值税',
  //   name: 'gridElectricityPriceWithTax',
  //   unit: '元/kwh',
  //   type: 'number',
  //   rules: [{ required: true, message: '请输入' }],
  //   visible: true
  // },
  {
    label: '上网电价不含税',
    name: 'gridElectricityPriceNoTax',
    unit: '元/kwh',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '水费单价不含税',
    name: 'waterPriceNoTax',
    unit: '元/吨',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '自备电厂含税电价',
    name: 'selfPowerPlantElectricityPriceWithTax',
    unit: '元/kwh',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '自备电厂不含税电价',
    name: 'selfPowerPlantElectricityPriceNoTax',
    unit: '元/kwh',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '下网不含税电价',
    name: 'gridHydrogenElectricityPriceNoTax',
    unit: '元/kwh',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '运输费率不含税',
    name: 'transportationRateNoTax',
    unit: '元/kg/公里',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '运输距离',
    name: 'transportDistance',
    unit: '公里',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '折现系数',
    name: 'discountRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '残值率',
    name: 'salvageRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 人工成本配置
export const formLaborCostConfig = () => [
  {
    label: '工人数量',
    name: 'workerCount',
    unit: '个',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '工人人均月薪',
    name: 'workerMonthlySalary',
    unit: '元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '技术人员数量',
    name: 'technicianCount',
    unit: '个',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '技术人均月薪',
    name: 'technicianMonthlySalary',
    unit: '元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '管理人员数量',
    name: 'managerCount',
    unit: '个',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '管理人均月薪',
    name: 'managerMonthlySalary',
    unit: '元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '福利社保系数',
    name: 'socialSecurityAndWelfareFactor',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '薪资年增长率',
    name: 'salaryAnnualGrowthRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 土地租赁配置
export const formLandLeaseConfig = () => [
  {
    label: '首年土地租赁费',
    name: 'photovoltaicLandRentalFeeYear2',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '土地租赁费增长率',
    name: 'photovoltaicLandRentalFeeGrowthRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '年度土地税费',
    name: 'photovoltaicLandTaxAnnual',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  }
]

// 其他配置（保持向后兼容）
export const formOtherConfig = () => [
  ...formOperationConfig(),
  ...formLaborCostConfig(),
  ...formLandLeaseConfig()
]

// 融资配置
export const formFinancingConfig = () => [
  {
    label: '基础借款利率',
    name: 'baseLoanRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '资本金比例',
    name: 'equityRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '融资比例',
    name: 'financingRatioBase',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '股东分红占比',
    name: 'shareholderDividendRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '法定盈余公积金率',
    name: 'statutorySurplusReserveFundRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  // {
  //   label: '补贴弹性时期',
  //   name: 'subsidyFlexibilityPeriod',
  //   unit: '年',
  //   type: 'number',
  //   rules: [{ required: true, message: '请输入' }],
  //   visible: true
  // },
  // {
  //   label: '还款方式',
  //   name: 'repaymentMethod',
  //   unit: '',
  //   type: 'select',
  //   rules: [{ required: true, message: '请选择' }],
  //   visible: true,
  //   options: [
  //     { label: '等额本息', value: 1 }
  //   ]
  // },
  // {
  //   label: '融资比例',
  //   name: 'financingRatio',
  //   unit: '%',
  //   type: 'number',
  //   rules: [{ required: true, message: '请输入' }],
  //   numberType: 'ratio',
  //   visible: true
  // }
]

// 税率配置
export const formTaxConfig = () => [
  {
    label: '维修费增值税率',
    name: 'maintenanceCostVATRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '材料费增值税率',
    name: 'materialsCostVATRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '运输费增值税率',
    name: 'transportationFeeVATRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '水费增值税率',
    name: 'waterFeeVATRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '水资源税单价',
    name: 'waterResourceTaxUnitPrice',
    unit: '元/吨',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '电费增值税率',
    name: 'electricityFeeVATRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '收入项增值税率',
    name: 'revenueItemVATRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '城市建设维护税率',
    name: 'cityConstructionAndMaintenanceTaxRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '教育费附加税率',
    name: 'educationSurchargeRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '地方教育费附加税率',
    name: 'localEducationSurchargeRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '增值税率',
    name: 'VATRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '安装税率',
    name: 'installationTaxRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '所得税率',
    name: 'incomeTaxRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 风机配置
const windInvestmentConfig = [
  {
    label: '容量',
    name: 'windCapacity',
    unit: 'MW',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '设备购置费',
    name: 'windTurbineEquipment',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '安装工程费',
    name: 'windTurbineInstallation',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '建筑工程费',
    name: 'windTurbineBuilding',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '其他费用',
    name: 'windTurbineOthers',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  }
]

const windCostConfig = [
  {
    label: '材料费计提率',
    name: 'windTurbineMaterialsCostProvisionRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '风机运维首年维修费率',
    name: 'windTurbineMaintCostRateYear2',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '风机保险费率',
    name: 'windTurbineInsuranceRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '风机其他费用单价',
    name: 'windTurbineOtherCostUnitPrice',
    unit: '元/kw',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  }
]

const windProfitConfig = [
  // {
  //   label: '第2年衰减率',
  //   name: 'windYearTwoDegradationPercentage',
  //   unit: '%',
  //   type: 'number',
  //   rules: [{ required: true, message: '请输入' }],
  //   numberType: 'ratio',
  //   visible: true
  // },
  // {
  //   label: '衰减率增长率',
  //   name: 'windYearNDegradationPercentage',
  //   unit: '%',
  //   type: 'number',
  //   rules: [{ required: true, message: '请输入' }],
  //   numberType: 'ratio',
  //   visible: true
  // }
  {
    label: '首年发电小时数(h)',
    name: 'windFirstYearPowerGenerationHour',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true,
    // notShowFromCapacity: true
  },
]

// 电网配置
// 配置
const gridProfitConfig = [
  {
    label: '发电制氢比例',
    name: 'powerToHydrogenRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true,
    // notShowFromCapacity: true
  },
]

// 储能配置
const storageInvestmentConfig = [
  {
    label: '容量',
    name: 'batCapacity',
    unit: 'MWh',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '设备购置费',
    name: 'energyStorageEquipment',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '安装工程费',
    name: 'energyStorageInstallation',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '建筑工程费',
    name: 'energyStorageBuilding',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '其他费用',
    name: 'energyStorageOthers',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  }
]

const storageCostConfig = [
  {
    label: '材料费计提率',
    name: 'energyStorageMaterialsCostProvisionRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '储能运维首年维修费率',
    name: 'energyStorageMaintCostRateYear2',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '储能保险费率',
    name: 'energyStorageInsuranceRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '储能其他费用单价',
    name: 'energyStorageOtherCostUnitPrice',
    unit: '元/kw',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  }
]

const storageProfitConfig = [
  // {
  //   label: '第2年衰减率',
  //   name: 'storageYearTwoDegradationPercentage',
  //   unit: '%',
  //   type: 'number',
  //   rules: [{ required: true, message: '请输入' }],
  //   numberType: 'ratio',
  //   visible: true
  // },
  // {
  //   label: '衰减率增长率',
  //   name: 'storageYearNDegradationPercentage',
  //   unit: '%',
  //   type: 'number',
  //   rules: [{ required: true, message: '请输入' }],
  //   numberType: 'ratio',
  //   visible: true
  // }
]

// 制氢配置
const hydrogenInvestmentConfig = [
  {
    label: '容量',
    name: 'alkCapacity',
    unit: 'MWh',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '设备购置费',
    name: 'electrolyzerEquipment',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '安装工程费',
    name: 'electrolyzerInstallation',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '建筑工程费',
    name: 'electrolyzerBuilding',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '其他费用',
    name: 'electrolyzerOthers',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  }
]

const hydrogenCostConfig = [
  {
    label: '材料费计提率',
    name: 'electrolyzerMaterialsCostProvisionRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '制氢运维首年维修费率',
    name: 'electrolyzerMaintCostRateYear2',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '制氢保险费率',
    name: 'electrolyzerInsuranceRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '制氢其他费用单价',
    name: 'electrolyzerOtherCostUnitPrice',
    unit: '元/kw',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  }
]

const hydrogenProfitConfig = [
  {
    label: '电解槽平均电耗',
    name: 'electrolyzerPowerConsumption',
    unit: 'kwh/Nm³',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  }
]

// 储氢配置
const hydrogenStorageInvestmentConfig = [
  {
    label: '容量',
    name: 'alkStoreCapacity',
    unit: 'm³',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '设备购置费',
    name: 'hydrogenStorageEquipment',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '安装工程费',
    name: 'hydrogenStorageInstallation',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '建筑工程费',
    name: 'hydrogenStorageBuilding',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '其他费用',
    name: 'hydrogenStorageOthers',
    unit: '万元',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  }
]

const hydrogenStorageCostConfig = [
  {
    label: '材料费计提率',
    name: 'hydrogenStorageMaterialsCostProvisionRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '储氢运维首年维修费率',
    name: 'hydrogenStorageMaintCostRateYear2',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '储氢保险费率',
    name: 'hydrogenStorageInsuranceRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '储氢其他费用单价',
    name: 'hydrogenStorageOtherCostUnitPrice',
    unit: '元/kw',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  }
]

const hydrogenStorageProfitConfig = [
  // {
  //   label: 'test_sunyi',
  //   name: 'test_sunyi',
  //   unit: '%',
  //   type: 'number',
  //   rules: [{ required: true, message: '请输入' }],
  //   numberType: 'ratio',
  //   visible: true
  // }
]

// 获取资源配置
export const getResourceConfig = (module, section) => {
  if (module === 'pv') {
    switch (section) {
      case 'investment':
        return pvInvestmentConfig
      case 'cost':
        return pvCostConfig
      case 'profit':
        return pvProfitConfig
      default:
        return []
    }
  } else if (module === 'wind') {
    switch (section) {
      case 'investment':
        return windInvestmentConfig
      case 'cost':
        return windCostConfig
      case 'profit':
        return windProfitConfig
      default:
        return []
    }
  } else if (module === 'grid') {
    switch (section) {
      case 'config':
        return gridProfitConfig
      default:
        return []
    }
  } else if (module === 'storage') {
    switch (section) {
      case 'investment':
        return storageInvestmentConfig
      case 'cost':
        return storageCostConfig
      case 'profit':
        return storageProfitConfig
      default:
        return []
    }
  } else if (module === 'hydrogen') {
    switch (section) {
      case 'investment':
        return hydrogenInvestmentConfig
      case 'cost':
        return hydrogenCostConfig
      case 'profit':
        return hydrogenProfitConfig
      default:
        return []
    }
  } else if (module === 'hydrogenStorage') {
    switch (section) {
      case 'investment':
        return hydrogenStorageInvestmentConfig
      case 'cost':
        return hydrogenStorageCostConfig
      case 'profit':
        return hydrogenStorageProfitConfig
      default:
        return []
    }
  }
  return []
}

// 风机辅助计算配置
const windInvestmentAuxConfig = [
  {
    label: '风机EPC',
    name: 'windEPC',
    unit: '元/W',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    visible: true
  },
  {
    label: '设备费占比',
    name: 'windEquipmentRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '安装费占比',
    name: 'windInstallRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '建筑费占比',
    name: 'windBuildingRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '其他费占比',
    name: 'windOthersRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 储能辅助计算配置
const storageInvestmentAuxConfig = [
  {
    label: '储能EPC',
    name: 'batEPC',
    unit: '元/Wh',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    visible: true
  },
  {
    label: '设备费占比',
    name: 'batEquipmentRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '安装费占比',
    name: 'batInstallRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '建筑费占比',
    name: 'batBuildingRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '其他费占比',
    name: 'batOthersRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 制氢辅助计算配置
const hydrogenInvestmentAuxConfig = [
  {
    label: '制氢EPC',
    name: 'alkEPC',
    unit: '元/W',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    visible: true
  },
  {
    label: '设备费占比',
    name: 'alkEquipmentRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '安装费占比',
    name: 'alkInstallRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '建筑费占比',
    name: 'alkBuildingRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '其他费占比',
    name: 'alkOthersRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 储氢辅助计算配置
const hydrogenStorageInvestmentAuxConfig = [
  {
    label: '储氢EPC',
    name: 'alkStoreEPC',
    unit: '元/m³',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    visible: true
  },
  {
    label: '设备费占比',
    name: 'alkStoreEquipmentRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '安装费占比',
    name: 'alkStoreInstallRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '建筑费占比',
    name: 'alkStoreBuildingRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '其他费占比',
    name: 'alkStoreOthersRatio',
    unit: '%',
    type: 'number',
    rules: [{ required: false, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 获取辅助计算配置
export const getAuxCalculateConfig = (module, section) => {
  if (module === 'pv' && section === 'investment') {
    return pvInvestmentAuxConfig
  } else if (module === 'wind' && section === 'investment') {
    return windInvestmentAuxConfig
  } else if (module === 'storage' && section === 'investment') {
    return storageInvestmentAuxConfig
  } else if (module === 'hydrogen' && section === 'investment') {
    return hydrogenInvestmentAuxConfig
  } else if (module === 'hydrogenStorage' && section === 'investment') {
    return hydrogenStorageInvestmentAuxConfig
  }
  return []
}

// 风机扩展参数配置
const windCostExtendConfig = [
  {
    label: '风机折旧年限',
    name: 'windTurbineDepreciationYears',
    unit: '年',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '风机大修更换费率',
    name: 'windTurbineMajorOverhaulReplacementRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '风机材料费年均增长率',
    name: 'windTurbineMaterialsCostAnnualGrowthRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '风机第7到11年维修费年增长率',
    name: 'windTurbineMaintCostGrowthRateYears7to11',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '风机第12到N年维修费年增长率',
    name: 'windTurbineMaintCostGrowthRateYears12plus',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '风机其他费用年增长率',
    name: 'windTurbineOtherCostGrowthRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

const windProfitExtendConfig = [
  {
    label: '发电率',
    name: 'windTestFadian',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 制氢成本扩展参数配置
const hydrogenCostExtendConfig = [
  {
    label: '制氢折旧年限',
    name: 'electrolyzerDepreciationYears',
    unit: '年',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '制氢大修更换费率',
    name: 'electrolyzerMajorOverhaulReplacementRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '制氢材料费年均增长率',
    name: 'electrolyzerMaterialsCostAnnualGrowthRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '制氢第7到11年维修费年增长率',
    name: 'electrolyzerMaintCostGrowthRateYears7to11',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '制氢第12到N年维修费年增长率',
    name: 'electrolyzerMaintCostGrowthRateYears12plus',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '制氢其他费用年增长率',
    name: 'electrolyzerOtherCostGrowthRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 制氢损益及其他扩展参数配置
const hydrogenProfitExtendConfig = [
  {
    label: 'test_alk1',
    name: 'test_alk1',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 储氢成本扩展参数配置
const hydrogenStorageCostExtendConfig = [
  {
    label: '储氢折旧年限',
    name: 'hydrogenStorageDepreciationYears',
    unit: '年',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '储氢大修更换费率',
    name: 'hydrogenStorageMajorOverhaulReplacementRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '储氢材料费年均增长率',
    name: 'hydrogenStorageMaterialsCostAnnualGrowthRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '储氢第7到11年维修费年增长率',
    name: 'hydrogenStorageMaintCostGrowthRateYears7to11',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '储氢第12到N年维修费年增长率',
    name: 'hydrogenStorageMaintCostGrowthRateYears12plus',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '储氢其他费用年增长率',
    name: 'hydrogenStorageOtherCostGrowthRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 储能成本扩展参数配置
const storageCostExtendConfig = [
  {
    label: '储能折旧年限',
    name: 'energyStorageDepreciationYears',
    unit: '年',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    visible: true
  },
  {
    label: '储能大修更换费率',
    name: 'energyStorageMajorOverhaulReplacementRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '储能材料费年均增长率',
    name: 'energyStorageMaterialsCostAnnualGrowthRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '储能第7到11年维修费年增长率',
    name: 'energyStorageMaintCostGrowthRateYears7to11',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '储能第12到N年维修费年增长率',
    name: 'energyStorageMaintCostGrowthRateYears12plus',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  },
  {
    label: '储能其他费用年增长率',
    name: 'energyStorageOtherCostGrowthRate',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 储能损益及其他扩展参数配置
const storageProfitExtendConfig = [
  {
    label: '储能效率',
    name: 'storageEfficiency',
    unit: '%',
    type: 'number',
    rules: [{ required: true, message: '请输入' }],
    numberType: 'ratio',
    visible: true
  }
]

// 获取扩展参数配置
export const getExtendConfig = (module, section) => {
  if (module === 'pv') {
    switch (section) {
      case 'cost':
        return pvCostExtendConfig
      case 'profit':
        return pvProfitExtendConfig
      default:
        return []
    }
  } else if (module === 'wind') {
    switch (section) {
      case 'cost':
        return windCostExtendConfig
      case 'profit':
        return windProfitExtendConfig
      default:
        return []
    }
  } else if (module === 'storage') {
    switch (section) {
      case 'cost':
        return storageCostExtendConfig
      case 'profit':
        return storageProfitExtendConfig
      default:
        return []
    }
  } else if (module === 'hydrogen') {
    switch (section) {
      case 'cost':
        return hydrogenCostExtendConfig
      case 'profit':
        return hydrogenProfitExtendConfig
      default:
        return []
    }
  } else if (module === 'hydrogenStorage') {
    switch (section) {
      case 'cost':
        return hydrogenStorageCostExtendConfig
      default:
        return []
    }
  }
  return []
} 