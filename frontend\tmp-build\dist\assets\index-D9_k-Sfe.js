import{i as A,l as R}from"./lodash-BnsPkmok.js";import{_ as V,r as x,o as O,a as T,b as n,c as m,u as W,d as k,e as t,f as _,w as a,g as Z,t as z,h as I,F,i as P,j as b,k as S,l as E,p as U,m as q}from"./index-D4CjQwBG.js";import{g as G}from"./index-DIXaVCf5.js";import"./index-CNS3ZdVi.js";const J="/assets/step1-BAZEOJx6.png",M="/assets/step2-DSLmiWlE.png",H="/assets/step3-CagpBwa6.png",K={__name:"index",props:["data"],setup(v,{expose:f}){const s=x(),l=x([40,50]),h=v;let i,d={grid:{top:"5%",bottom:"5%",left:"3%",right:"3%",containLabel:!0},xAxis:{type:"category",data:["风光_储能_网_制_储","风光_网_制","风光_储能_网_制","风光_储能_制_储","风光_储能_网_制","风光_网_制","风光_制_储","风光_储能_制_储"],axisLabel:{rotate:56,fontSize:12,marginRight:0}},yAxis:{type:"value",boundaryGap:!1},series:[{data:[120,200,150,80,70,110,130,200],type:"bar",barWidth:"50%"}]};const y=()=>{i.resize()};return O(()=>{i=A(s.value),i.setOption({...d,...h.data});const e=R.throttle(()=>y(),300);window.addEventListener("resize",e),i.on("datazoom",o=>{var p;const c=((p=o==null?void 0:o.batch)==null?void 0:p[0])||o;l.value=[c.start,c.end]}),setTimeout(()=>{y()},2e3)}),T(()=>{i.clear(),d={...d,...h.data},i.clear(),d.dataZoom[0].start=l.value[0],d.dataZoom[0].end=l.value[1],i.setOption(d,!0)}),f({resize:y}),(e,o)=>(n(),m("div",{class:"line_chart",ref_key:"container",ref:s},null,512))}},Q=V(K,[["__scopeId","data-v-68238e13"]]),r=v=>(U("data-v-f6b675c6"),v=v(),q(),v),X={class:"body_wrap"},Y={class:"part_wrap"},tt=r(()=>t("div",{class:"title1"}," 首页 ",-1)),st={key:0,class:"content_wrap"},et={class:"box_wrap box_recent"},at=r(()=>t("div",{class:"b_title"},"近期测算项目",-1)),it={class:"b_body"},ot={class:"b_name"},ct={class:"b_k_v_item"},dt=r(()=>t("div",{class:"key"},"客户名称：",-1)),_t={class:"val"},nt={class:"b_k_v_item"},lt=r(()=>t("div",{class:"key"},"项目背景：",-1)),rt={class:"val"},vt={class:"see_detail"},pt={class:"box_wrap box_mine"},ut=r(()=>t("div",{class:"b_title"},"我的项目",-1)),bt={class:"b_body"},mt={key:0},ft={key:1},ht={class:"see_detail"},yt={class:"box_wrap box_stat"},gt=r(()=>t("div",{class:"b_title"},"项目统计",-1)),kt={class:"b_body"},xt=r(()=>t("span",null," 暂无数据 ",-1)),wt=Z('<div class="part_wrap" data-v-f6b675c6><div class="title1" data-v-f6b675c6> 功能指引 </div><div class="step_wrap" data-v-f6b675c6><div data-v-f6b675c6>容量测算 </div><div class="step_item_wrap" data-v-f6b675c6><div class="step_item" data-v-f6b675c6><div class="number_wrap" data-v-f6b675c6>01</div><div class="line_wrap" data-v-f6b675c6></div></div><div class="step_item" data-v-f6b675c6><div class="number_wrap" data-v-f6b675c6>02</div><div class="line_wrap" data-v-f6b675c6></div></div><div class="step_item" data-v-f6b675c6><div class="number_wrap" data-v-f6b675c6>03</div><div class="line_wrap" data-v-f6b675c6></div></div></div><div class="step_content_wrap" data-v-f6b675c6><div class="s_c_item" data-v-f6b675c6><div class="s_c_title" data-v-f6b675c6>新建测算项目</div><div class="s_c_desc" data-v-f6b675c6>在项目测算页面，通过新建项目操作可以创建新项目，或基于项目创建新方案</div><img class="s_c_img" src="'+J+'" height="80%" data-v-f6b675c6></div><div class="s_c_item" data-v-f6b675c6><div class="s_c_title" data-v-f6b675c6>编辑项目参数</div><div class="s_c_desc" data-v-f6b675c6>在创建页面，输入项目信息，选择求解算法以及场景，配置相关参数</div><img class="s_c_img" src="'+M+'" height="80%" data-v-f6b675c6></div><div class="s_c_item" data-v-f6b675c6><div class="s_c_title" data-v-f6b675c6>获取测算方案</div><div class="s_c_desc" data-v-f6b675c6>测算完成后，方案结果页面展示绿电、电网、制氢、储氢容量等信息，以及功率等曲线信息，并支持修改配置结果再次测算</div><img class="s_c_img" src="'+H+'" height="80%" data-v-f6b675c6></div></div></div></div>',1),zt={__name:"index",setup(v){const f=W(),s=x([]),l=x(!1),h=x({}),i=[{title:"方案名称",dataIndex:"name",key:"name"},{title:"客户名称",dataIndex:"customer",key:"customer"}],d=async()=>{l.value=!0;const{code:y,data:{total:e,result:o},msg:c}=await G({pageNumber:0,pageSize:5}),p={1:{number:0},2:{number:2},3:{number:3},4:{number:5},5:{number:7},6:{number:8},7:{number:3},8:{number:10}};h.value={series:[{data:Object.values(p).map(C=>C.number),type:"bar",barWidth:"50%"}]},s.value=o,l.value=!1,console.log("r:",s)};return O(()=>{d()}),(y,e)=>{const o=k("a-tag"),c=k("a-button"),p=k("a-table"),C=k("a-empty"),$=k("a-spin");return n(),m("div",X,[t("div",Y,[tt,_($,{spinning:l.value},{default:a(()=>{var j,B,D,L,N;return[(j=s.value)!=null&&j.length?(n(),m("div",st,[t("div",et,[at,t("div",it,[t("div",ot,z((B=s.value[0])==null?void 0:B.name),1),t("div",ct,[dt,t("div",_t,z((D=s.value[0])==null?void 0:D.customer),1)]),t("div",nt,[lt,t("div",rt,z((L=s.value[0])==null?void 0:L.desc),1)])]),t("div",vt,[t("a",{type:"link",href:"void:0",onClick:e[0]||(e[0]=g=>{var w;return I(f).push({name:"projectDetail",query:{projectId:(w=s.value[0])==null?void 0:w.id}})})},"查看详情>")])]),t("div",pt,[ut,t("div",bt,[_(p,{columns:i,"data-source":s.value.slice(0,5),pagination:!1,size:"small"},{bodyCell:a(({column:g,record:w})=>[g.key==="tags"?(n(),m("span",mt,[(n(!0),m(F,null,P(w.tags,u=>(n(),E(o,{key:u,color:u==="loser"?"volcano":(u==null?void 0:u.length)>5?"geekblue":"green"},{default:a(()=>[b(z(u.toUpperCase()),1)]),_:2},1032,["color"]))),128))])):g.key==="action"?(n(),m("span",ft,[_(c,{type:"link",size:"small"},{default:a(()=>[b("详情")]),_:1}),_(c,{type:"link",size:"small"},{default:a(()=>[b("暂停/启动")]),_:1}),_(c,{type:"link",size:"small"},{default:a(()=>[b("删除")]),_:1}),_(c,{type:"link",size:"small"},{default:a(()=>[b("配置")]),_:1})])):S("",!0)]),_:1},8,["data-source"])]),t("div",ht,[t("a",{type:"link",href:"void:0",onClick:e[1]||(e[1]=g=>I(f).push({name:"projectList"}))},"查看详情>")])]),t("div",yt,[gt,t("div",kt,[_(Q,{data:h.value},null,8,["data"])])])])):S("",!0),(N=s.value)!=null&&N.length?S("",!0):(n(),E(C,{key:1},{description:a(()=>[xt]),default:a(()=>[_(c,{type:"primary",onClick:e[2]||(e[2]=g=>I(f).push({name:"createProject"}))},{default:a(()=>[b("创建新项目")]),_:1})]),_:1}))]}),_:1},8,["spinning"])]),wt])}}},Bt=V(zt,[["__scopeId","data-v-f6b675c6"]]);export{Bt as default};
