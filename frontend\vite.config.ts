import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
// import vueJsx from '@vitejs/plugin-vue-jsx'
import VueDevTools from 'vite-plugin-vue-devtools'
import { expressPlugin } from './mockServer'

// 环境配置
const envConfig = {
  prod: {
    target: "http://10.58.2.172:9099",
    outDir: "dist_dev_cap_v2"
  },
  test: {
    target: "http://10.58.2.172:8099",
    outDir: "dist_dev_cap_v2_test"
  },
  zh: {
    target: "http://10.58.2.172:8099",
    outDir: "dist_dev_cap_v2_test_zh"
  }
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  // 获取当前环境配置，默认为test环境
  const currentEnv = envConfig[mode as keyof typeof envConfig] || envConfig.test

  return {
    plugins: [
      vue(),
      // vueJsx(),
      // VueDevTools(),
      // expressPlugin()
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    define: {
      // 将环境变量暴露给客户端代码
      __APP_ENV__: JSON.stringify(env.VITE_APP_ENV),
      __API_BASE_URL__: JSON.stringify(env.VITE_API_BASE_URL)
    },
    server: {
      host: '0.0.0.0',
      proxy: {
        "/api/v1": {
          target: env.VITE_API_BASE_URL || currentEnv.target,
          changeOrigin: true,
          ws: true
        },
      },
    },
    build: {
      outDir: currentEnv.outDir
    }
  }
})
