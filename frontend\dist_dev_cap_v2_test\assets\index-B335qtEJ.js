import{c as Z}from"./index-B_PAPayX.js";import{h as I,U as G,_ as ee,d as te,v as ne,r as $,e as D,o as ae,f as C,b as k,c as g,g as _,w as O,m as V,F as R,l as ie,p as L,A as F,n as E,t as x,i as B,H as se,D as le,E as oe}from"./index-k3sxFAFL.js";var re={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"}}]},name:"star",theme:"filled"};const ce=re;function M(m){for(var c=1;c<arguments.length;c++){var d=arguments[c]!=null?Object(arguments[c]):{},b=Object.keys(d);typeof Object.getOwnPropertySymbols=="function"&&(b=b.concat(Object.getOwnPropertySymbols(d).filter(function(u){return Object.getOwnPropertyDescriptor(d,u).enumerable}))),b.forEach(function(u){ue(m,u,d[u])})}return m}function ue(m,c,d){return c in m?Object.defineProperty(m,c,{value:d,enumerable:!0,configurable:!0,writable:!0}):m[c]=d,m}var j=function(c,d){var b=M({},c,d.attrs);return I(G,M({},b,{icon:ce}),null)};j.displayName="StarFilled";j.inheritAttrs=!1;const me=j;var de={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M574 665.4a8.03 8.03 0 00-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 00-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 000 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 000 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 00-11.3 0L372.3 598.7a8.03 8.03 0 000 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z"}}]},name:"link",theme:"outlined"};const fe=de;function q(m){for(var c=1;c<arguments.length;c++){var d=arguments[c]!=null?Object(arguments[c]):{},b=Object.keys(d);typeof Object.getOwnPropertySymbols=="function"&&(b=b.concat(Object.getOwnPropertySymbols(d).filter(function(u){return Object.getOwnPropertyDescriptor(d,u).enumerable}))),b.forEach(function(u){pe(m,u,d[u])})}return m}function pe(m,c,d){return c in m?Object.defineProperty(m,c,{value:d,enumerable:!0,configurable:!0,writable:!0}):m[c]=d,m}var z=function(c,d){var b=q({},c,d.attrs);return I(G,q({},b,{icon:fe}),null)};z.displayName="LinkOutlined";z.inheritAttrs=!1;const ye=z,P=m=>(le("data-v-706a2248"),m=m(),oe(),m),_e={class:"body_wrap"},he={class:"p_wrap"},ve={class:"content_wrap"},be={class:"part_wrap"},ke={class:"p_title"},ge={class:"title-wrapper"},Se=P(()=>_("div",{class:"btn_wrap"},null,-1)),xe=P(()=>_("div",{style:{"font-size":"16px","font-weight":"700",margin:"20px 0"}},"指标对比",-1)),Ie={class:"table_wrap"},Oe={key:0,class:"loading-state"},Fe=P(()=>_("div",{class:"loading-text"},"正在加载方案对比数据...",-1)),Pe={key:0,class:"solution-header"},we={key:0,class:"unit-text"},Ce={key:0,class:"value-cell"},Te={key:1,class:"parent-cell"},$e=P(()=>_("div",{class:"empty-state"},[_("div",{class:"empty-text"},"暂无方案对比数据")],-1)),Re={key:0,class:"analysis_wrap"},Le=P(()=>_("div",{class:"analysis_title"}," 方案分析 ",-1)),Ee={class:"analysis_content"},je={class:"analysis_label"},ze={class:"analysis_value"},Ne={__name:"index",setup(m){const c=te(),d=ne(),b=$(!1),u=$([]),T=$([]),H=D(()=>{const i=[{title:"财务指标",key:"indicator",dataIndex:"name",width:240,fixed:"left"}],l=u.value.length;let e=150;return l<=2?e=200:l===3?e=180:l===4?e=160:l===5&&(e=140),u.value.forEach((n,h)=>{var s,t;const p=((s=n.solution)==null?void 0:s.name)||`方案${h+1}`;i.push({title:p,key:`solution_${((t=n.solution)==null?void 0:t.id)||h}`,width:e,align:"center",customTitle:!0,solutionData:n})}),i}),N=D(()=>{if(u.value.length===0)return[];const i=[{key:"averageAnnualGeneration",name:"年均发电量",unit:"万kW·h",isSingle:!0},{key:"investment",name:"项目总投资",children:[{key:"fixedAssetsStaticInvestment",name:"固定资产静态投资",unit:"万元"},{key:"staticInvestmentPerWatt",name:"单瓦静态总投资",unit:"元/W"},{key:"dynamicInvestmentPerWatt",name:"单瓦动态总投资",unit:"元/W"},{key:"investmentCostPerKWh",name:"度电投资成本",unit:"元/kWh"}]},{key:"cost",name:"总成本费用",children:[{key:"averageAnnualOperatingCost",name:"年平均运营成本",unit:"万元"},{key:"totalGenerationCostPerUnit",name:"单位发电总成本",unit:"元/kWh"},{key:"operatingCostPerKWh",name:"度电运营成本",unit:"元/kWh"},{key:"lcoh",name:"平准化制氢成本LCOH",unit:"元/Nm³",optimize:"min"},{key:"lcoe",name:"平准化度电成本LCOE",unit:"元/kWh"}]},{key:"profitability",name:"项目投资财务内部收益率_税后",children:[{key:"projectInvestmentFIRR_afterTax",name:"项目投资财务内部收益率(税后)",unit:"%"},{key:"equityCapitalFIRR_afterTax",name:"资本金财务内部收益率(税后)",unit:"%"},{key:"projectInvestmentNPV_beforeTax",name:"项目投资财务税前净现值",unit:"万元"},{key:"equityCapitalNPV_afterTax",name:"资本金财务税后净现值",unit:"万元"},{key:"returnOnInvestment",name:"总投资收益率ROI",unit:"%"},{key:"returnOnEquity",name:"项目资本金净利润率ROE",unit:"%"}]},{key:"paybackPeriodAfterTax_static",name:"税后投资回收期(静态)",unit:"年",isSingle:!0,optimize:"min"},{key:"paybackPeriodAfterTax_dynamic",name:"税后投资回收期(动态)",unit:"年",isSingle:!0}],l=[];return i.forEach(e=>{if(e.isSingle){const n={key:e.key,name:e.name,unit:e.unit,optimize:e.optimize,isSingle:!0},h=[];if(u.value.forEach((p,s)=>{var f,o,y;const t=`solution_${((f=p.solution)==null?void 0:f.id)||s}`;let a=(((y=(o=p.result)==null?void 0:o.resultTables)==null?void 0:y.financialIndicatorsSummary)||{})[e.key]||0;e.unit==="%"?a<1&&a>0?a=(a*100).toFixed(2):a=a.toFixed(2):typeof a=="number"&&(a=a.toFixed(2)),n[t]=a,h.push({key:t,value:parseFloat(a)||0})}),e.optimize){const p=w(h,e.optimize);n.bestSolutions=p}l.push(n)}else{const n={key:e.key,name:e.name,children:[],isParent:!0};let h="",p="";if(e.key==="investment"?(h="projectTotalInvestment",p="万元"):e.key==="cost"?(h="averageAnnualOperatingCost",p="万元"):e.key==="profitability"&&(h="projectInvestmentFIRR_afterTax",p="%"),h){n.unit=p;const s=[];if(u.value.forEach((t,S)=>{var y,r,v;const a=`solution_${((y=t.solution)==null?void 0:y.id)||S}`;let o=(((v=(r=t.result)==null?void 0:r.resultTables)==null?void 0:v.financialIndicatorsSummary)||{})[h]||0;p==="%"?o<1&&o>0?o=(o*100).toFixed(2):o=o.toFixed(2):typeof o=="number"&&(o=o.toFixed(2)),n[a]=o,s.push({key:a,value:parseFloat(o)||0})}),e.key==="investment"){const t=w(s,"min");n.bestSolutions=t}else if(e.key==="profitability"){const t=w(s,"max");n.bestSolutions=t}}else u.value.forEach((s,t)=>{var a;const S=`solution_${((a=s.solution)==null?void 0:a.id)||t}`;n[S]=""});e.children.forEach(s=>{const t={key:s.key,name:s.name,unit:s.unit,optimize:s.optimize,isChild:!0},S=[];if(u.value.forEach((a,f)=>{var v,A,W;const o=`solution_${((v=a.solution)==null?void 0:v.id)||f}`;let r=(((W=(A=a.result)==null?void 0:A.resultTables)==null?void 0:W.financialIndicatorsSummary)||{})[s.key]||0;s.unit==="%"?r<1&&r>0?r=(r*100).toFixed(2):r=r.toFixed(2):typeof r=="number"&&(r=r.toFixed(2)),t[o]=r,S.push({key:o,value:parseFloat(r)||0})}),s.optimize){const a=w(S,s.optimize);t.bestSolutions=a}n.children.push(t)}),l.push(n)}}),l}),w=(i,l)=>{if(i.length===0)return[];let e;return l==="max"?e=Math.max(...i.map(n=>n.value)):e=Math.min(...i.filter(n=>n.value>0).map(n=>n.value)),i.filter(n=>n.value===e).map(n=>n.key)},K=(i,l)=>{if(i==null||i==="")return"-";const e=parseFloat(i);return isNaN(e)?i:e===0?"0":i},U=async i=>{if(!(!i||i.length===0)){b.value=!0;try{const{code:l,data:e,msg:n}=await Z(i);l===0?(u.value=e,J(),console.log("API返回数据:",e)):F.error(n)}catch(l){F.error("获取对比数据失败"),console.error("error",l)}finally{b.value=!1}}},J=()=>{if(u.value.length===0)return;const i=[];let l=-1,e=null;if(u.value.forEach(a=>{var o,y,r,v;const f=((r=(y=(o=a.result)==null?void 0:o.resultTables)==null?void 0:y.financialIndicatorsSummary)==null?void 0:r.projectInvestmentFIRR_afterTax)||0;f>l&&(l=f,e=(v=a.solution)==null?void 0:v.name)}),e&&l>0){const a=l<1?(l*100).toFixed(2):l.toFixed(2);i.push({label:"项目收益率最高",value:`${e}（${a}%）`})}let n=1/0,h=null;if(u.value.forEach(a=>{var o,y,r,v;const f=((r=(y=(o=a.result)==null?void 0:o.resultTables)==null?void 0:y.financialIndicatorsSummary)==null?void 0:r.projectTotalInvestment)||0;f>0&&f<n&&(n=f,h=(v=a.solution)==null?void 0:v.name)}),h){const a=n>=1e4?`${(n/1e4).toFixed(2)}亿元`:`${n.toFixed(2)}万元`;i.push({label:"总投资最少",value:`${h}（${a}）`})}let p=1/0,s=null;u.value.forEach(a=>{var o,y,r,v;const f=((r=(y=(o=a.result)==null?void 0:o.resultTables)==null?void 0:y.financialIndicatorsSummary)==null?void 0:r.paybackPeriodAfterTax_static)||0;f>0&&f<p&&(p=f,s=(v=a.solution)==null?void 0:v.name)}),s&&i.push({label:"静态回收期最短",value:`${s}（${p.toFixed(2)}年）`});let t=1/0,S=null;u.value.forEach(a=>{var o,y,r,v;const f=((r=(y=(o=a.result)==null?void 0:o.resultTables)==null?void 0:y.financialIndicatorsSummary)==null?void 0:r.lcoh)||0;f>0&&f<t&&(t=f,S=(v=a.solution)==null?void 0:v.name)}),S&&i.push({label:"平准化制氢成本LCOH最低",value:`${S}（${t.toFixed(2)}元/Nm³）`}),T.value=i},Q=()=>{c.go(-1)},X=i=>{var n;if(!((n=i==null?void 0:i.solution)!=null&&n.id)){F.error("方案数据不完整");return}const l=i.solution.projectId||i.projectId,e=i.solution.id;if(!l){F.error("缺少项目ID");return}c.push({name:"economicDetail",params:{projectId:l,solutionId:e}})},Y=()=>{const i=d.query.solutionIds;if(!i){F.error("缺少方案参数");return}const l=i.split(",").map(e=>parseInt(e)).filter(e=>!isNaN(e));if(l.length===0){F.error("方案参数格式错误");return}U(l)};return ae(()=>{Y()}),(i,l)=>{const e=C("a-breadcrumb-item"),n=C("a-breadcrumb"),h=C("a-spin"),p=C("a-table");return k(),g("div",_e,[_("div",he,[_("div",ve,[_("div",be,[_("div",ke,[_("div",ge,[I(n,null,{default:O(()=>[I(e,null,{default:O(()=>[_("a",{href:"void:0",onClick:Q}," < 返回")]),_:1}),I(e,null,{default:O(()=>[E("方案对比")]),_:1})]),_:1})]),Se]),xe,_("div",Ie,[b.value||u.value.length===0||N.value.length===0?(k(),g("div",Oe,[I(h,{size:"large"}),Fe])):(k(),V(p,{key:1,size:"small",bordered:"",class:"compare_table",columns:H.value,"data-source":N.value,pagination:!1,"row-key":s=>s.key,defaultExpandAllRows:!0},{headerCell:O(({column:s})=>[s.customTitle?(k(),g("div",Pe,[_("span",null,x(s.title),1),I(B(ye),{class:"detail-link-icon",onClick:t=>X(s.solutionData),title:"查看详情"},null,8,["onClick"])])):(k(),g(R,{key:1},[E(x(s.title),1)],64))]),bodyCell:O(({column:s,record:t})=>[s.key==="indicator"?(k(),g("span",{key:0,class:se(["indicator-name",{"parent-indicator":t.isParent,"child-indicator":t.isChild,"single-indicator":t.isSingle}])},[E(x(t.name)+" ",1),t.unit?(k(),g("span",we,"（"+x(t.unit)+"）",1)):L("",!0)],2)):(k(),g(R,{key:1},[t.isChild||t.isSingle||t.isParent&&t.unit?(k(),g("div",Ce,[_("span",null,x(K(t[s.key],t.unit)),1),t.bestSolutions&&t.bestSolutions.includes(s.key)?(k(),V(B(me),{key:0,class:"best-icon"})):L("",!0)])):(k(),g("div",Te))],64))]),emptyText:O(()=>[$e]),_:1},8,["columns","data-source","row-key"]))]),T.value.length>0?(k(),g("div",Re,[Le,_("div",Ee,[(k(!0),g(R,null,ie(T.value,(s,t)=>(k(),g("div",{class:"analysis_item",key:t},[_("div",je,x(s.label)+"：",1),_("div",ze,x(s.value),1)]))),128))])])):L("",!0)])])])])}}},De=ee(Ne,[["__scopeId","data-v-706a2248"]]);export{De as default};
