# 电价配置模块问题修复说明

## 修复的问题

### 1. 数据回显问题

**问题描述**：
- 当前已有地区数据包含电价，但选中地区后无法正确回显电价数据
- 需要根据月份范围正确推断period类型（年度、半年、季度、月度）

**修复方案**：

#### 1.1 改进period类型推断逻辑
```javascript
const determinePeriodFromData = (yearPriceData) => {
  // 检查是否为整年数据（1-12月）
  if (yearPriceData.length === 1) {
    const group = yearPriceData[0]
    if (group.begin === 1 && group.end === 12) return 'year'
  }
  
  // 检查是否为半年数据（1-6月，7-12月）
  if (yearPriceData.length === 2) {
    const hasFirstHalf = yearPriceData.some(group => group.begin === 1 && group.end === 6)
    const hasSecondHalf = yearPriceData.some(group => group.begin === 7 && group.end === 12)
    if (hasFirstHalf && hasSecondHalf) return 'halfYear'
  }
  
  // 检查是否为季度数据（1-3，4-6，7-9，10-12月）
  if (yearPriceData.length === 4) {
    const expectedQuarters = [
      { begin: 1, end: 3 }, { begin: 4, end: 6 },
      { begin: 7, end: 9 }, { begin: 10, end: 12 }
    ]
    const hasAllQuarters = expectedQuarters.every(quarter =>
      yearPriceData.some(group => group.begin === quarter.begin && group.end === quarter.end)
    )
    if (hasAllQuarters) return 'quarter'
  }
  
  // 检查是否为月度数据（12个月）
  if (yearPriceData.length === 12) {
    const hasAllMonths = Array.from({ length: 12 }, (_, i) => i + 1).every(month =>
      yearPriceData.some(group => group.begin === month && group.end === month)
    )
    if (hasAllMonths) return 'month'
  }
  
  return 'quarter' // 默认值
}
```

#### 1.2 优化树选择事件
```javascript
const handleTreeSelect = (keys) => {
  selectedKeys.value = keys
  // 重置到第一个月份组，确保显示数据
  activeMonthGroup.value = 0
  
  // 添加调试信息
  if (keys.length > 0) {
    const selectedConfig = priceConfigs.value.find(config => config.id.toString() === keys[0])
    console.log('选中的地区配置:', selectedConfig)
  }
}
```

### 2. 年份处理问题

**问题描述**：
- 数据格式中的2024为示例，实际数据中年份可能不同
- 需要获取第一个年份的数据，而不是固定使用当前年份
- 提交数据时需要保持年份一致

**修复方案**：

#### 2.1 获取数据时使用第一个可用年份
```javascript
const getConfigedZone = async() => {
  // 获取第一个年份的电价数据（不一定是当前年份）
  const availableYears = Object.keys(price)
  const firstYear = availableYears.length > 0 ? availableYears[0] : new Date().getFullYear().toString()
  const yearPriceData = price[firstYear] || []
  
  return {
    // ...其他字段
    currentYear: firstYear, // 保存当前使用的年份
    groupedTouPriceByMonth: yearPriceData.map(monthGroup => ({
      begin: monthGroup.begin,
      end: monthGroup.end,
      groupedTouPriceByDay: monthGroup.groupedTouPriceByDay
    }))
  }
}
```

#### 2.2 提交数据时保持年份一致
```javascript
const updateConfigedZone = async(configData) => {
  // 使用配置数据中保存的年份，如果没有则使用当前年份
  const targetYear = configData.currentYear || new Date().getFullYear().toString()
  
  const submitData = {
    zone: { /* zone信息 */ },
    price: {
      [targetYear]: configData.groupedTouPriceByMonth.map(monthGroup => ({
        begin: monthGroup.begin,
        end: monthGroup.end,
        groupedTouPriceByDay: monthGroup.groupedTouPriceByDay
      }))
    }
  }
}
```

#### 2.3 新增地区时设置年份
```javascript
const handleRegionConfirm = async (regionData) => {
  const newConfig = {
    ...regionData,
    currentYear: new Date().getFullYear().toString(), // 新增地区使用当前年份
    // ...其他字段
  }
}
```

## 数据流程验证

### 1. 获取数据流程
1. API返回格式：`{ zone: {...}, price: { "2024": [...] } }`
2. 提取第一个年份的数据
3. 根据月份范围推断period类型
4. 转换为组件内部格式
5. 在界面上正确显示

### 2. 保存数据流程
1. 用户编辑电价数据
2. 保存到本地配置
3. 转换为API格式（保持原年份）
4. 提交到后端
5. 更新成功后显示提示

### 3. 数据回显验证
- 整年数据（1-12月）→ period: 'year'
- 半年数据（1-6月，7-12月）→ period: 'halfYear'  
- 季度数据（1-3，4-6，7-9，10-12月）→ period: 'quarter'
- 月度数据（12个单独月份）→ period: 'month'

## 测试建议

1. **数据回显测试**：
   - 创建不同period类型的测试数据
   - 验证选中地区后能正确显示电价配置
   - 验证月份标签和电价图表显示正确

2. **年份处理测试**：
   - 测试包含非当前年份数据的地区
   - 验证获取和保存时年份处理正确
   - 测试新增地区的年份设置

3. **边界情况测试**：
   - 空数据处理
   - 不完整的月份数据
   - 异常的月份范围

## 调试信息

在开发过程中添加了调试信息，可以在浏览器控制台查看：
- 选中地区时的配置信息
- 电价数据结构
- 推断的period类型

生产环境中可以移除这些调试信息。
