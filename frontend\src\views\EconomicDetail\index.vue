<template>
  <div class="economic-detail">
    <!-- 顶部标题和操作按钮 -->
    <div class="header-section">
      <div class="title-wrapper">
        <a-breadcrumb>
          <a-breadcrumb-item><a href="void:0" @click="router.back()"> < 经济分析</a></a-breadcrumb-item>
          <a-breadcrumb-item>分析结果</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      <div class="header-actions">
        <a-button class="action-btn" @click="downloadReport" :loading="loading">下载报告</a-button>
        <a-button type="primary" class="action-btn" @click="goToReportDetail">查看报告</a-button>
      </div>
    </div>

    <a-spin :spinning="loading" :tip="tips">
      <ProjectInfo :solution-info="solutionInfo" />

      <KeyIndicators :result-data="resultData" />

      <TabTables :result-data="resultData" />

      <SensitivityAnalysis :sensitivity-data="sensitivityData" />
    </a-spin>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getEconomicSolutionResult, getEconomicSolutionInfo } from '@/api/economic'
import ProjectInfo from './components/ProjectInfo.vue'
import KeyIndicators from './components/KeyIndicators.vue'
import TabTables from './components/TabTables.vue'
import SensitivityAnalysis from './components/SensitivityAnalysis.vue'
import { downloadFile, strToBase64 } from '@/util'

const router = useRouter()
const route = useRoute()
const resultData = ref(null)
const sensitivityData = ref(null)
const solutionInfo = ref({})
const loading = ref(false)
const tips = ref('数据加载中...')

const getResultData = async () => {
  try {
    loading.value = true
    const { code, data, msg} = await getEconomicSolutionResult({
      projectId: route.params.projectId,
      solutionId: route.params.solutionId
    })
    console.log('接口返回数据:', data)
    console.log('lcoh:', data.resultTables.financialIndicatorsSummary.lcoh)
    console.log('Excel原 lcoh_ori:', data.resultTables.financialIndicatorsSummary.lcoh_ori)
    console.log('lcoe:', data.resultTables.financialIndicatorsSummary.lcoe)
    console.log('Excel原 lcoe_ori:', data.resultTables.financialIndicatorsSummary.lcoe_ori)
    console.log('敏感性分析数据:', data.resultAnalysis)
    if (code === 0) {
      resultData.value = data.resultTables;
      // 设置敏感性分析数据
      sensitivityData.value = data.resultAnalysis || null;
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    resultData.value = null
  } finally {
    loading.value = false
  }
}

const getSolutionInfo = async () => {
  try {
    const { code, data, msg } = await getEconomicSolutionInfo({
      projectId: route.params.projectId,
      solutionId: route.params.solutionId
    })
    if (code === 0) {
      solutionInfo.value = data
    }
  } catch (error) {
    console.error('获取方案信息失败:', error)
  }
}
// 跳转到方案详情页
const goToReportDetail = () => {
  router.push({
    path: '/report-detail',
    query: {
      projectId: route.params.projectId,
      solutionId: route.params.solutionId
    }
  })
}

const downloadReport = async () => {
  loading.value = true
  tips.value = '报告生成中...'
  const str = await strToBase64(`${location.origin}/report-detail?projectId=${route.params.projectId}&solutionId=${route.params.solutionId}&type=1`)
  await downloadFile(`/api/v1/cecp/result/export?encodedUrl=${str}`, `${solutionInfo.value?.project?.name}.pdf`)

  setTimeout(() => {
    loading.value = false
  }, 6500);
}

onMounted(async () => {
  await Promise.all([
    getResultData(),
    getSolutionInfo()
  ])
})
</script>

<style scoped lang="less">
@import '@/style/base.less';

.economic-detail {
  padding: 16px;
  background: #f0f2f5;
  min-height: 100vh;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  border-radius: 4px;
}

.page-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  height: 32px;
  padding: 0 15px;
  font-size: 14px;
}

.title-wrapper {
  flex: 1;
}
</style> 