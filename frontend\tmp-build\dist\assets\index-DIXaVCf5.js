import{g as s,p as c,u as t}from"./index-CNS3ZdVi.js";const n=async a=>await s(`${t}/capacity/getProject`,a),r=async a=>await s(`${t}/capacity/getResult`,a),i=async a=>await s(`${t}/capacity/getSolution`,a),o=async a=>await s(`${t}/config/getForecast`,a),g=async a=>await s(`${t}/config/getDevParams`,a),u=async()=>await s(`${t}/config/getH2AbsorbConfig`),y=async()=>await s(`${t}/config/getGridPrice`),m=async a=>await c(`${t}/capacity/submitTask`,a),f=async a=>await c(`${t}/capacity/saveSolution`,a),p=async a=>await c(`${t}/capacity/mdfCalcParams`,a),w=async a=>await c(`${t}/capacity/modifyProject`,a);export{r as a,m as b,i as c,o as d,g as e,y as f,n as g,u as h,w as i,p as m,f as s};
