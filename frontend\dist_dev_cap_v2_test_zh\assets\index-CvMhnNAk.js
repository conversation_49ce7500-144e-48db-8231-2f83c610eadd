import{_ as Ee,r as m,k as Pe,G as Ke,e as ze,f as k,b as o,m as c,w as i,g as l,c as w,l as I,H as K,h as _,t as L,F as q,p as V,n as D,d as ia,v as da,x as ca,o as _a,A as me,i as d,I as va,B as F,C as O,J as pa,D as ma,E as fa}from"./index-mrm9ST-C.js";import{D as y}from"./decimal-A899wnYr.js";import{e as ga,f as ba,h as He,d as Ve,i as ya,j as ha}from"./index-CgDCw0ka.js";import{g as Ze}from"./index-DpPqV0rE.js";import{L as We}from"./index-gDJNP4Zk.js";import{l as Ca}from"./lodash-CmcO15ls.js";/* empty css                                                              */const wa="/assets/pv-Dnj8ONCj.svg",ka="data:image/svg+xml,%3csvg%20width='36'%20height='36'%20viewBox='0%200%2036%2036'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M12.9906%2014.2562C12.2136%2014.3687%2011.6722%2015.0964%2011.7812%2015.8874C11.8902%2016.6784%2012.6074%2017.2269%2013.3843%2017.1179C14.1683%2016.9983%2014.7062%2016.2706%2014.5937%2015.4866C14.4847%2014.6956%2013.7675%2014.1472%2012.9906%2014.2562ZM11.8691%2013.6093L12.4878%2013.444C12.6074%2013.4124%2012.7339%2013.3878%2012.864%2013.3737C13.2261%2013.3315%2013.5847%2013.3562%2013.9363%2013.444L14.5515%2013.5987L14.78%2013.0081C15.1949%2011.9394%2015.4339%2010.3292%2015.4339%208.59248C15.4339%208.33584%2015.4093%202.34521%2013.2085%202.34521C11.0078%202.34521%2010.9831%208.33936%2010.9831%208.59248C10.9831%2010.3292%2011.2222%2011.9394%2011.6371%2013.0116L11.8691%2013.6093ZM12.0695%2017.8913L11.6476%2017.4448C11.5562%2017.3464%2011.4718%2017.2444%2011.391%2017.1354L11.3874%2017.1319C11.1695%2016.8331%2011.0078%2016.4991%2010.9093%2016.144L10.7371%2015.5358L10.1113%2015.6308C8.98627%2015.803%207.49565%2016.3972%206.01557%2017.2655C5.7976%2017.3921%200.710489%2020.412%201.80033%2022.3315C1.91283%2022.5284%202.14135%2022.7921%202.59838%2022.9116C2.77416%2022.9573%202.97104%2022.9784%203.18549%2022.9784C4.53549%2022.9784%206.61322%2022.1206%208.25151%2021.1573C9.73158%2020.289%2010.9831%2019.2729%2011.6898%2018.3694L12.0695%2017.8913ZM20.3347%2017.3042C18.8546%2016.4323%2017.3605%2015.8382%2016.239%2015.6659L15.6203%2015.5745L15.4445%2016.1757C15.4058%2016.3093%2015.3601%2016.4394%2015.3074%2016.5659C15.1632%2016.9034%2014.9628%2017.2128%2014.7062%2017.48L14.2808%2017.9265L14.6605%2018.4116C15.3706%2019.3187%2016.6222%2020.3347%2018.0988%2021.1995C19.7371%2022.1628%2021.8113%2023.0206%2023.1648%2023.0206C23.3792%2023.0206%2023.5761%2022.9995%2023.7519%2022.9538C24.2054%2022.8308%2024.4374%2022.5706%2024.5499%2022.3702C25.6433%2020.4472%2020.5527%2017.4308%2020.3347%2017.3042ZM12.3789%2018.5628L11.1413%2033.6554H15.23L13.996%2018.5628H12.3789Z'%20fill='%23575D6C'/%3e%3cpath%20d='M25.7626%2018.9067C25.193%2018.9876%2024.7958%2019.522%2024.8766%2020.1021C24.9575%2020.6821%2025.4813%2021.0864%2026.0508%2021.0021C26.6239%2020.9142%2027.0211%2020.3798%2026.9368%2019.8067C26.8559%2019.2302%2026.3321%2018.8259%2025.7626%2018.9067ZM24.9364%2018.4321L25.3899%2018.3091C25.4778%2018.2845%2025.5692%2018.2704%2025.6641%2018.2563C25.9278%2018.2247%2026.195%2018.2423%2026.4516%2018.3091L26.9016%2018.4216L27.0704%2017.9892C27.3762%2017.2052%2027.5485%2016.0239%2027.5485%2014.7513C27.5485%2014.5649%2027.5309%2010.1704%2025.9137%2010.1704C24.3001%2010.1704%2024.2825%2014.5649%2024.2825%2014.7513C24.2825%2016.0239%2024.4583%2017.2052%2024.7606%2017.9892L24.9364%2018.4321ZM31.145%2021.1427C30.0586%2020.5028%2028.9653%2020.0669%2028.1391%2019.9438L27.6856%2019.8771L27.559%2020.32C27.5309%2020.4185%2027.4958%2020.5134%2027.4571%2020.6048C27.3516%2020.8544%2027.204%2021.0794%2027.0176%2021.2763L26.7083%2021.6032L26.986%2021.9583C27.5063%2022.6228%2028.4239%2023.3681%2029.5067%2024.0009C30.709%2024.7075%2032.2313%2025.3368%2033.2227%2025.3368C33.3809%2025.3368%2033.5251%2025.3192%2033.6516%2025.2876C33.9856%2025.1997%2034.1543%2025.0063%2034.2352%2024.8622C35.0368%2023.4489%2031.3067%2021.2341%2031.145%2021.1427ZM25.3126%2022.0638L24.4055%2033.6548H27.4043L26.4973%2022.0638H25.3126ZM21.9305%2023.5228C20.4926%2023.3892%2019.4168%2022.8724%2018.777%2022.454C17.9122%2023.1993%2017.1528%2024.1204%2017.5606%2024.8341C17.6415%2024.9782%2017.8102%2025.1716%2018.1442%2025.2595C18.2708%2025.2946%2018.4149%2025.3087%2018.5731%2025.3087C19.5645%2025.3087%2021.0868%2024.6794%2022.2891%2023.9728C22.4473%2023.8813%2022.595%2023.7829%2022.7426%2023.6845C22.4508%2023.6353%2022.1801%2023.5825%2021.9305%2023.5228Z'%20fill='%23575D6C'/%3e%3c/svg%3e",xa="data:image/svg+xml,%3csvg%20width='36'%20height='36'%20viewBox='0%200%2036%2036'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M19.313%203.61717L21.5284%203L29.7283%2031.1897L27.5107%2031.8069L19.313%203.61717ZM4.24561%2030.9854L12.222%203L14.4374%203.61717L6.46315%2031.6026L4.24773%2030.9854H4.24561Z'%20fill='%23575D6C'/%3e%3cpath%20d='M5.35205%2027.4867L25.0738%2016.9927L26.1804%2018.8442L6.46083%2029.336L5.35418%2027.4845L5.35205%2027.4867Z'%20fill='%23575D6C'/%3e%3cpath%20d='M7.79116%2018.8442L8.8978%2016.9927L28.6195%2027.4846L27.5129%2029.3361L7.79116%2018.8442ZM22.6372%2014.109L29.2835%207.93735L30.8371%209.37599L24.1887%2015.5477L22.6351%2014.109H22.6372ZM2.91553%209.37811L4.46696%207.93735L11.1132%2014.1112L9.56392%2015.5519L2.91553%209.38024V9.37811ZM12.222%203H21.5285V5.05794H12.222V3Z'%20fill='%23575D6C'/%3e%3cpath%20d='M2.91504%207.32007H30.6131V9.37801H2.91504V7.32007ZM8.67598%2012.466H24.8522V14.5218H8.67598V12.466Z'%20fill='%23575D6C'/%3e%3c/svg%3e",La="data:image/svg+xml,%3csvg%20width='36'%20height='36'%20viewBox='0%200%2036%2036'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M14.4944%2019.7507H17.692L16.9482%2024.2446C16.935%2024.3228%2016.9491%2024.4031%2016.9879%2024.4723C17.0267%2024.5415%2017.0879%2024.5953%2017.1615%2024.6249C17.2054%2024.6414%2017.2492%2024.6523%2017.2929%2024.6523C17.4051%2024.6523%2017.5117%2024.5975%2017.5801%2024.5017L22.4816%2017.4994C22.5188%2017.4474%2022.5408%2017.3861%2022.5452%2017.3224C22.5495%2017.2586%2022.536%2017.1949%2022.5062%2017.1384C22.4766%2017.0819%2022.4323%2017.0345%2022.3778%2017.0013C22.3234%2016.968%2022.261%2016.9502%2022.1972%2016.9497H18.7945L19.7353%2013.1833C19.755%2013.1055%2019.7473%2013.0233%2019.7134%2012.9505C19.6795%2012.8778%2019.6216%2012.8189%2019.5494%2012.784C19.4772%2012.7494%2019.3955%2012.7403%2019.3175%2012.7582C19.2395%2012.7761%2019.1699%2012.8199%2019.12%2012.8824L14.2183%2019.1874C14.1784%2019.2394%2014.1537%2019.3014%2014.1469%2019.3666C14.1401%2019.4317%2014.1516%2019.4975%2014.18%2019.5566C14.2093%2019.6149%2014.2542%2019.6639%2014.3097%2019.6981C14.3652%2019.7324%2014.4292%2019.7506%2014.4944%2019.7507Z'%20fill='%23575D6C'/%3e%3cpath%20d='M30.2493%207.84698H28.1486V6.09627C28.1486%205.51926%2027.6754%205.0459%2027.0982%205.0459C26.5211%205.0459%2026.0477%205.51911%2026.0477%206.09627V7.84698H22.3141V6.09627C22.3141%205.51926%2021.8409%205.0459%2021.2637%205.0459C20.6866%205.0459%2020.2134%205.51911%2020.2134%206.09627V7.84698H16.4801V6.09627C16.4801%205.51926%2016.0069%205.0459%2015.4296%205.0459C14.8524%205.0459%2014.3792%205.51911%2014.3792%206.09627V7.84698H10.6428V6.09627C10.6428%205.51926%2010.1696%205.0459%209.59243%205.0459C9.01543%205.0459%208.54221%205.51911%208.54221%206.09627V7.84698H5.74129C4.77576%207.84698%203.99072%208.63186%203.99072%209.59739V27.1032C3.99072%2028.0687%204.77576%2028.8536%205.74129%2028.8536H8.19204V30.2543C8.1924%2030.4399%208.26628%2030.6178%208.39751%2030.749C8.52875%2030.8802%208.70664%2030.9541%208.89224%2030.9545H13.0936C13.2792%2030.9541%2013.457%2030.8802%2013.5883%2030.749C13.7195%2030.6178%2013.7934%2030.4399%2013.7938%2030.2543V28.8536H22.1965V30.2543C22.1969%2030.4399%2022.2708%2030.6178%2022.402%2030.749C22.5333%2030.8802%2022.7111%2030.9541%2022.8967%2030.9545H27.0982C27.2838%2030.9541%2027.4617%2030.8802%2027.5929%2030.7489C27.7241%2030.6177%2027.798%2030.4398%2027.7984%2030.2543V28.8536H30.2493C31.2148%2028.8536%2031.9999%2028.0687%2031.9999%2027.1032V9.59739C32%208.63453%2031.2148%207.84698%2030.2493%207.84698ZM29.8991%2026.7531H6.09146V9.94741H29.8991V26.7531Z'%20fill='%23575D6C'/%3e%3c/svg%3e",Va="/assets/alk-C4Vh8w2f.svg",qa="/assets/chuqing-BkpeVjyR.svg",Ua=G=>[{label:"项目名称",name:"projectName",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"string"},{label:"客户名称",name:"customer",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"string"},{label:"项目周期",name:"cycle",unit:"年",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"年产氢量",name:"h2Product",unit:"吨",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"项目描述",name:"desc",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"textarea"}],Ia=()=>[{label:"LCOH最低",name:"target0",unit:"",default:1,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"投资成本最低",name:"target1",unit:"",default:0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"弃电率最低",name:"target2",unit:"",default:0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"产氢量最大",name:"target3",unit:"",default:0,rules:[{required:!1,message:"请输入"}],type:"number"}],za=()=>[{label:"快速测算",name:"",default:1}],Da=()=>[{label:"EPC投资",name:"pv_epc",unit:"元/W",default:2.9,rules:[{required:!0,message:"请输入"}],type:"number",visible:!0,tag:0},{label:"运维成本",name:"pv_om_cost",unit:"元/W/年",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",visible:!0,tag:0}],Sa=()=>[{label:"EPC投资",name:"wind_epc",unit:"元/W",default:4.2,rules:[{required:!0,message:"请输入"}],type:"number",visible:!0,tag:1},{label:"运维成本",name:"wind_om_cost",unit:"元/W/年",default:.3,rules:[{required:!1,message:"请输入"}],type:"number",visible:!0,tag:1}],$a=()=>[{label:"用水价格",name:"water_price",unit:"元/吨",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number"},{label:"制氢耗水量",name:"h2_water_consuming",unit:"L/Nm³",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"制氢策略",name:"ele_policy",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"select",options:[{label:"满功率",value:1},{label:"均分",value:2},{label:"轮值",value:3}]},{label:"运维比例",name:"h2_om_radio",unit:"%",default:.02,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio",visible:!0,tag:4}],Ta=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:""},{title:"类型",dataIndex:"ele_type",key:"ele_type",s_type:"select",rules:[{required:!1}],unit:"",options:[{label:"ALK",value:1,color:"blue"},{label:"PEM",value:2,color:"green"}]},{title:"容量",dataIndex:"capacity"},{title:"系统价格(元/套)",dataIndex:"price"},{title:"电耗(kWh/Nm³)",dataIndex:"power_consumption"},{title:"额定功率(MW)",dataIndex:"pe"},{title:"最低负载率",dataIndex:"lower_load_rate"},{title:"最高负载率",dataIndex:"upper_load_rate"},{title:"辅助系统能耗(kWh/Nm³)",dataIndex:"assist_consumption"}],Ma=()=>[{label:"年最大下网比例",name:"grid_down_radio",unit:"%",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",max:1,numberType:"ratio"},{label:"年最大上网比例",name:"grid_up_radio",unit:"%",default:.2,rules:[{required:!1,message:"请输入"}],type:"number",max:1,numberType:"ratio"},{label:"绿电上网价格(元/kwh)",name:"grid_sale_price",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"}],Ra=()=>[{label:"控制策略",name:"es_policy",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"select",options:[{label:"削峰填谷",value:1}]},{label:"最低配置比例",name:"es_min_radio",unit:"",default:.1,rules:[{required:!1,message:"请输入"}],type:"number",max:1,numberType:"ratio"},{label:"EPC投资",name:"es_epc",unit:"元/Wh",default:1.3,rules:[{required:!0,message:"请输入"}],type:"number",visible:!0,tag:3},{label:"运维成本",name:"es_om_cost",unit:"元/Wh/年",default:.0675,rules:[{required:!1,message:"请输入"}],type:"number",visible:!0,tag:3}],Pa=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:""},{title:"单机功率(MW)",dataIndex:"single_power"},{title:"充电效率",dataIndex:"charge_efficiency"},{title:"放电效率",dataIndex:"discharge_efficiency"},{title:"充放电倍率",dataIndex:"c_rate"},{title:"初始SOC",dataIndex:"init_soc"},{title:"SOC下限",dataIndex:"min_soc"},{title:"SOC上限",dataIndex:"max_soc"},{title:"寿命(年)",dataIndex:"life_cycle"}],Ha=()=>[{label:"最大升负荷速率",name:"max_increase_load_rate",unit:"%",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio"},{label:"最大降负荷速率",name:"max_down_load_rate",unit:"%",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio"},{label:"每小时供氢上限",name:"max_supply_h2",unit:"Nm³",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"每小时供氢下限",name:"min_supply_h2",unit:"Nm³",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"负荷调整时间",name:"adjust_time",unit:"min",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"负荷调节间隔",name:"adjust_interval",unit:"min",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"每小时供氢量",name:"supply_h2",unit:"Nm³",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"EPC投资",name:"hs_invest",unit:"元/m³",default:2,rules:[{required:!0,message:"请输入"}],type:"number",visible:!0,tag:5},{label:"运维比例(%)",name:"hs_om_radio",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio",visible:!0,tag:5}],Za=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:""},{title:"体积(m³)",dataIndex:"volume"},{title:"最小运行压力(Mpa)",dataIndex:"min_pressure"},{title:"最大运行压力(Mpa)",dataIndex:"max_pressure"},{title:"价格(元)",dataIndex:"price"},{title:"占地面积(㎡)",dataIndex:"area"}],Wa=()=>[{label:"光伏EPC投资",name:"pv_epc",unit:"元/W",default:2.9,rules:[{required:!0,message:"请输入"}],type:"number",visible:!0,tag:0},{label:"风电EPC投资",name:"wind_epc",unit:"元/W",default:4.2,rules:[{required:!0,message:"请输入"}],type:"number",visible:!0,tag:1},{label:"储能EPC投资",name:"es_epc",unit:"元/Wh",default:1.3,rules:[{required:!0,message:"请输入"}],type:"number",visible:!0,tag:3},{label:"制氢系统投资",name:"h2_invest",unit:"元/W",default:2,rules:[{required:!0,message:"请输入"}],type:"number",visible:!0,tag:4},{label:"制氢厂房投资",name:"plant_invest",unit:"元/W",default:1.5,rules:[{required:!0,message:"请输入"}],type:"number",visible:!0,tag:4},{label:"储氢系统投资",name:"hs_invest",unit:"元/Nm³",default:2,rules:[{required:!0,message:"请输入"}],type:"number",visible:!0,tag:5}],ja=()=>[{label:"光伏运维比例",name:"pv_om_radio",unit:"",default:.25,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio",visible:!0,tag:0},{label:"光伏运维成本",name:"pv_om_cost",unit:"元/W/年",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",visible:!0,tag:0},{label:"风电运维比例",name:"wind_om_radio",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio",visible:!0,tag:1},{label:"风电运维成本",name:"wind_om_cost",unit:"元/W/年",default:.3,rules:[{required:!1,message:"请输入"}],type:"number",visible:!0,tag:1},{label:"储能运维比例",name:"es_om_radio",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio",visible:!0,tag:3},{label:"储能运维成本",name:"es_om_cost",unit:"元/Wh/年",default:.0675,rules:[{required:!1,message:"请输入"}],type:"number",visible:!0,tag:3},{label:"制氢运维比例",name:"h2_om_radio",unit:"",default:.02,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio",visible:!0,tag:4},{label:"制氢运维成本",name:"h2_om_cost",unit:"元/W/年",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",visible:!0,tag:4},{label:"储氢运维比例",name:"hs_om_radio",unit:"",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio",visible:!0,tag:5},{label:"储氢运维成本",name:"hs_om_cost",unit:"元/kg/年",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",visible:!0,tag:5},{label:"设备折现率",name:"discount_rate",unit:"",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number",numberType:"ratio",visible:!0,tag:-1},{label:"负荷缺失率",name:"load_miss",unit:"",default:0,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio",visible:!0,tag:-1}],Ea=()=>[{label:"贷款周期",name:"loanCycle",unit:"年",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number"},{label:"贷款比例",name:"loanRadio",unit:"%",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio"},{label:"贷款利率",name:"loanRate",unit:"%",default:void 0,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio"},{label:"设备折现率",name:"discount_rate",unit:"%",default:void 0,rules:[{required:!0,message:"请输入"}],type:"number",numberType:"ratio",tag:-1},{label:"负荷缺失率",name:"load_miss",unit:"%",default:0,rules:[{required:!1,message:"请输入"}],type:"number",numberType:"ratio",visible:!0,tag:-1}],je={projectName:void 0,customer:void 0,desc:void 0,location:{address:"",lng:0,lat:0},h2Product:void 0,cycle:25,loanRadio:.7,loanCycle:15,loanRate:.031,solutionName:void 0,topology:[1,0,0,0,1,0],targetExpr:[1,0,0,0],algorithm:1,pv_min_capacity:0,pv_max_capacity:void 0,pv_dev_ids:void 0,pv_damp_rate:void 0,wind_min_capacity:0,wind_max_capacity:void 0,grid_down_radio:.1,grid_zone_id:void 0,grid_year:void 0,grid_sale_price:.332,grid_up_radio:.2,es_dev_ids:[],es_min_capacity:0,es_max_capacity:void 0,es_policy:1,es_min_radio:.1,ele_dev_ids:[],ele_min_capacity:0,ele_max_capacity:void 0,ele_policy:2,water_price:4.38,h2_water_consuming:1.4,hs_dev_ids:[],hs_min_capacity:0,hs_max_capacity:0,max_increase_load_rate:.2,max_down_load_rate:.2,adjust_time:void 0,adjust_interval:void 0,absorb_id:void 0,supply_h2:void 0,pv_forecast_zone_id:void 0,pv_forecast:void 0,wind_forecast_zone_id:void 0,wind_forecast:void 0,pv_epc:2.8,wind_epc:3.2,es_epc:.7,h2_invest:2,plant_invest:1.5,hs_invest:2500,discount_rate:.06,pv_om_radio:0,pv_om_cost:.05,wind_om_radio:0,wind_om_cost:.6,es_om_radio:0,es_om_cost:.04,h2_om_radio:.02,h2_om_cost:0,hs_om_radio:.02,hs_om_cost:0,load_miss:0},Ka={class:"power_list"},Aa=["onClick"],Na={class:"title"},Ba={class:"title"},Fa={__name:"index",props:["selfData","chartList","config"],emits:["submit"],setup(G,{expose:fe,emit:A}){const Z=G,a=m([40,50]),Ue=m({pvPower:[]});m(1);const j=m({});m([]);const C=m([]),$=m({desc:"自定义地区",id:-10,title:"",series:[{data:Z.selfData,type:"line",lineStyle:{normal:{color:Pe.baseColor}}}],grid:{top:"5%",bottom:"10%",left:"3%",right:"5%",containLabel:!0},dataZoom:Z.config.hasZoom?[{show:!0,realtime:!0,start:a.value[0],end:a.value[1],bottom:"0px"},{type:"inside"}]:null}),re=Ca.cloneDeep($.value),le=m(!1),ie=A,te=T=>{j.value=T};fe({open:()=>{le.value=!0},close:()=>{le.value=!1}});const de=()=>{ie("submit",j.value)};Ke(()=>Z.chartList,T=>{});const J=ze(()=>{var T;return j.value=Z.config.default||{},(T=Z.chartList)==null?void 0:T.map(R=>{const{yData:Q,unit:ce,desc:N,id:X}=R;return{desc:N,id:X,title:"",series:[{data:Q,type:"line",lineStyle:{normal:{color:Pe.baseColor}}}],grid:{top:"5%",bottom:"10%",left:"3%",right:"5%",containLabel:!0},dataZoom:Z.config.hasZoom?[{show:!0,realtime:!0,start:a.value[0],end:a.value[1],bottom:"0px"},{type:"inside"}]:null}})}),ne=T=>{const R=new FileReader;C.value=[],$.value=re,R.onload=Q=>{Q.target.result.split(/\r*\n+/).forEach(N=>{N&&C.value.push(parseFloat(N))}),console.log("file content:",C.value),$.value={...$.value,series:[{...$.value.series[0],data:C.value}]},j.value.id==-10&&(j.value.series[0].data=C.value),T.onSuccess()},R.readAsText(T.file)};return(T,R)=>{const Q=k("UploadOutlined"),ce=k("a-button"),N=k("a-upload"),X=k("a-modal");return o(),c(X,{width:"90%",open:le.value,"onUpdate:open":R[2]||(R[2]=W=>le.value=W),title:Z.config.modalTitle,onOk:de},{default:i(()=>{var W;return[l("div",Ka,[(W=J.value)!=null&&W.length?(o(!0),w(q,{key:0},I(J.value,P=>(o(),w("div",{class:K(["p_item",{p_item_sel:P.id===j.value.id}]),onClick:De=>te(P)},[_(We,{data:P,class:"chart_wrap"},null,8,["data"]),l("div",Na,L(P.desc),1)],10,Aa))),256)):V("",!0),Z.config.canUploadSelf?(o(),w("div",{key:1,class:K(["p_item",{p_item_sel:j.value.id===-10}]),onClick:R[1]||(R[1]=P=>te($.value))},[_(We,{data:$.value,class:"chart_wrap"},null,8,["data"]),l("div",Ba,[_(N,{onChange2:T.uploadChange,"before-upload2":P=>T.beforeUpload(P),customRequest:ne,onRemove:R[0]||(R[0]=()=>{Ue.value.pvPower=[]}),accept:".txt",maxCount:1},{default:i(()=>[_(ce,{size:"small",style:{fontSize:"11px",marginRight:"10px"}},{icon:i(()=>[_(Q)]),default:i(()=>[D(" 上传数据 ")]),_:1})]),_:1},8,["onChange2","before-upload2"]),D(" 自定义数据 ")])],2)):V("",!0)])]}),_:1},8,["open","title"])}}},qe=Ee(Fa,[["__scopeId","data-v-6f490c55"]]),h=G=>(ma("data-v-a96c4f0d"),G=G(),fa(),G),Oa={class:"body_wrap"},Ga={class:"part_wrap"},Ja={class:"content_wrap"},Qa={class:"box_wrap"},Xa=h(()=>l("div",{class:"b_title"},"项目信息",-1)),Ya={class:"b_body"},el={class:"line_item"},al={key:0},ll={class:"box_wrap"},tl=h(()=>l("div",{class:"b_title"},"求解方式",-1)),sl={class:"b_body"},nl={class:"goal_list"},ul=h(()=>l("div",{class:"g_title"},"求解目标选择",-1)),ol=["onClick","name","rules"],rl={class:"goal_item"},il={class:"name"},dl={class:"v_line"},cl={class:"number_input"},_l=h(()=>l("div",{class:"g_tips"},"选择多个求解目标后，需设置每个目标的权重，总权重值需为100%",-1)),vl={class:"goal_list"},pl=h(()=>l("div",{class:"g_title"},"求解算法",-1)),ml={class:"box_wrap"},fl=h(()=>l("div",{class:"b_title"},"场景选择",-1)),gl={class:"b_body"},bl={class:"scene_title_wrap"},yl={class:"pv_wind_wrap"},hl=h(()=>l("div",{class:"left"},[l("img",{src:wa})],-1)),Cl=h(()=>l("div",{class:"right"},[l("div",{class:"r_title"},"光伏"),l("div",{class:"r_desc"},"选择光伏场景后可配置光伏参数")],-1)),wl=[hl,Cl],kl=h(()=>l("div",{class:"left"},[l("img",{src:ka})],-1)),xl=h(()=>l("div",{class:"right"},[l("div",{class:"r_title"},"风机"),l("div",{class:"r_desc"},"选择风机场景后可配置风机参数")],-1)),Ll=[kl,xl],Vl=h(()=>l("div",{class:"left"},[l("img",{src:xa})],-1)),ql=h(()=>l("div",{class:"right"},[l("div",{class:"r_title"},"电网"),l("div",{class:"r_desc"},"选择电网场景后可配置电网用电")],-1)),Ul=[Vl,ql],Il=h(()=>l("div",{class:"left"},[l("img",{src:La})],-1)),zl=h(()=>l("div",{class:"right"},[l("div",{class:"r_title"},"储能"),l("div",{class:"r_desc"},"选择储能场景后可配置储能参数")],-1)),Dl=[Il,zl],Sl=h(()=>l("div",{class:"left"},[l("img",{src:Va})],-1)),$l=h(()=>l("div",{class:"right"},[l("div",{class:"r_title"},"制氢"),l("div",{class:"r_desc"},"选择制氢场景后可配置制氢参数")],-1)),Tl=[Sl,$l],Ml=h(()=>l("div",{class:"left"},[l("img",{src:qa})],-1)),Rl=h(()=>l("div",{class:"right"},[l("div",{class:"r_title"},"储氢"),l("div",{class:"r_desc"},"选择储氢场景后可配置储氢参数")],-1)),Pl=[Ml,Rl],Hl={class:"box_wrap"},Zl=h(()=>l("div",{class:"b_title"},"设备配置",-1)),Wl={class:"b_body"},jl={class:"scene_content_wrap"},El={class:"line_item"},Kl={class:"value"},Al={class:"desc_wrap"},Nl={class:"value"},Bl={class:"desc_wrap"},Fl={class:"value range_item"},Ol=h(()=>l("div",{class:"middle_line"},"—",-1)),Gl={class:"line_item"},Jl={class:"value"},Ql={class:"desc_wrap"},Xl={class:"value range_item"},Yl=h(()=>l("div",{class:"middle_line"},"—",-1)),et={class:"line_item"},at={class:"grid_price"},lt={key:0,class:"desc_wrap"},tt={class:"line_item"},st={class:"range_item"},nt=h(()=>l("div",{class:"middle_line"},"—",-1)),ut={class:"common_form_item"},ot=h(()=>l("div",{class:"i_label"},"储能选择",-1)),rt={class:"line_item"},it={class:"range_item"},dt=h(()=>l("div",{class:"middle_line"},"—",-1)),ct={class:"common_form_item"},_t=h(()=>l("div",{class:"i_label"},"电解槽选择",-1)),vt={class:"line_item"},pt={class:"common_form_item"},mt=h(()=>l("div",{class:"i_label"},"储氢罐选择",-1)),ft={class:"box_wrap"},gt=h(()=>l("div",{class:"b_title"},"其他配置",-1)),bt={class:"b_body"},yt={class:"line_item"},ht={class:"button_wrap"},Ct={__name:"index",setup(G){const fe=ia(),A=da(),Z=m();m(!1);const a=m({}),Ue=m(Wa()),j=m(ja()),C=ca({selectedAlkRowKeys:[],selectedBatRowKeys:[],loading:!1,running:!1}),$=m({alk:[],bat:[],alkStorage:[]}),re=m([{title:"光伏",key:0},{title:"风机",key:1},{title:"电网",key:2},{title:"储能",key:3},{title:"制氢",key:4},{title:"储氢",key:5}]);m([1,2]);const le=ze(()=>{const{projectId:n,solutionId:t}=A.query;return n?t?"修改方案":"新建方案":"创建项目"}),ie=m([]),te=m(),ge=m(),se=m({}),de=m({}),J=m({}),ne=m({}),T=m(),R=n=>{var t,p,b;te.value.close(),J.value=n,a.value.pv_forecast_zone_id=n.id,n.id==-10?(a.value.pv_forecast={data:(b=(p=(t=n==null?void 0:n.series)==null?void 0:t[0])==null?void 0:p.data)!=null&&b.length?n.series[0].data:void 0},a.value.pv_forecast_zone_id=-1):a.value.pv_forecast=null},Q=n=>{console.log("pv device:",n),ge.value.close(),ne.value=n,a.value.pv_dev_ids=[n.id]},ce=async()=>{const{code:n,msg:t,data:p}=await He({type:1});se.value=p.result.map(b=>{const{zone:r,forecast:g}=b,{id:x,region:f,country:v,province:H,city:U}=r;return{yData:g.data,unit:"",desc:[H,U].filter(z=>!!z).join("_"),id:x}})},N=async()=>{const{code:n,msg:t,data:p}=await Ve({type:1});de.value=p.result.map(b=>{const{baseInfo:{id:r,manufacturer:g,model:x},params:f}=b;return{yData:f.damp_curve.data,unit:"",desc:[g,x].filter(v=>!!v).join("_"),id:r}}),console.log("pv forecast:",se.value)},X=m();m();const W=m({});m({});const P=m({});m({});const De=n=>{var t,p,b;console.log("wind power:",n),X.value.close(),P.value=n,a.value.wind_forecast_zone_id=n.id,n.id==-10?(a.value.wind_forecast={data:(b=(p=(t=n==null?void 0:n.series)==null?void 0:t[0])==null?void 0:p.data)!=null&&b.length?n.series[0].data:void 0},a.value.wind_forecast_zone_id=-1):a.value.wind_forecast=null},Ae=async()=>{const{code:n,msg:t,data:p}=await He({type:2});W.value=p.result.map(b=>{const{zone:r,forecast:g}=b,{id:x,region:f,country:v,province:H,city:U}=r;return{yData:g.data,unit:"",desc:[H,U].filter(z=>!!z).join("_"),id:x}}),console.log("wind forecast:",W.value)},Se=(n,t)=>{t||(Y.value[n]=!Y.value[n]),a.value.targetExpr=Y.value.map((p,b)=>p?a.value[`target${b}`]:0),console.log("targetExpr:",a.value.targetExpr)},Ie=m([]),_e=m(4),Y=m([!1,!1,!1,!1]),M=m([!1,!1,!1,!1,!1,!1]),$e=n=>{Ue.value.forEach(t=>{t.tag===n&&(t.visible=!t.visible)}),j.value.forEach(t=>{t.tag===n&&(t.visible=!t.visible)})},ue=n=>{M.value[n]=!M.value[n],a.value.topology=M.value.map((t,p)=>t?1:0),Ie.value=re.value.filter((t,p)=>M.value[p]),M.value[n]?_e.value=n:_e.value=a.value.topology.findIndex(t=>!!t),$e(n)};ze(()=>C.selectedAlkRowKeys.length>0);const Ne=n=>{C.selectedAlkRowKeys=n,a.value.ele_dev_ids=n},Be=async()=>{const{code:n,msg:t,data:p}=await Ve({type:4});$.value.alk=p.result.map(b=>{const{baseInfo:r,params:g}=b;return{...r,...g}})},Fe=n=>{},be=m([]),ee=m({}),Oe=async()=>{const{code:n,msg:t,data:p}=await ya();be.value=p.result.map(b=>{const{price:r,zone:{city:g,country:x,id:f,province:v,region:H}}=b;return{label:[g].filter(U=>!!U).join("_"),value:f,children:Object.keys(r).map(U=>{const z=parseInt(U);return{label:z,value:z}})}}),console.log("grid price:",p.result,be.value)},Ge=(n,t)=>{console.log("slect price:",n,t),a.value.grid_zone_id=n[0],a.value.grid_year=n[1],ee.value=t},Je=async()=>{const{code:n,msg:t,data:p}=await Ve({type:3});$.value.bat=p.result.map(b=>{const{baseInfo:r,params:g}=b;return{...r,...g}})},Qe=n=>{console.log("selectedBatRowKeys changed: ",n),C.selectedBatRowKeys=n,a.value.es_dev_ids=n},Te=m([]),Me=m(),Xe=m({}),Ye=async()=>{const{code:n,msg:t,data:p}=await Ve({type:5});$.value.alkStorage=p.result.map(b=>{const{baseInfo:r,params:g}=b;return{...r,...g}})},ea=n=>{console.log("alk storage:",n),Me.value.close(),Xe.value=n,a.value.absorb_id=n.id},aa=n=>{console.log("selectedAlkStoreRowKeys changed: ",n),C.selectedAlkStoreRowKeys=n,a.value.hs_dev_ids=n},la=async()=>{const{code:n,msg:t,data:p}=await ha();Te.value=p.result.map(b=>{const{id:r,name:g,demandCurve:x}=b;return{yData:x.data,unit:"",desc:g,id:r}}),console.log("wind forecast:",W.value)},ta=()=>{var r,g,x,f;if(a.value.targetExpr.reduce((v,H)=>v+H)!==1)return me.warn("已选求解目标权重总和需为100% !"),!1;const t=a.value.pv_forecast_zone_id>0||((g=(r=a.value.pv_forecast)==null?void 0:r.data)==null?void 0:g.length),p=a.value.wind_forecast_zone_id>0||((f=(x=a.value.wind_forecast)==null?void 0:x.data)==null?void 0:f.length);return t||p?!0:(me.warn("光伏和风机出力曲线请至少选择1项!"),!1)},sa=async()=>{const n=await Z.value.validateFields(),t={...n,...a.value,hs_min_capacity:0,hs_max_capacity:0};if(["target0","target1","target2","target3","scene0","scene1","scene2","scene3","scene4","scene5"].forEach(f=>t.hasOwnProperty(f)&&delete t[f]),console.log("re:",t,n,a.value),!ta())return;C.loading=!0,C.running=!0;const{code:r,msg:g,data:x}=await ga(t);if(C.running=!1,C.loading=!1,r===0){me.success("已提交运行");const f={taskId:x.taskId},{projectId:v,solutionId:H}=A.query;v&&(f.projectId=v);const U=fe.resolve({name:"projectDetail",query:f});window.open(U.href,"_blank")}else me.error(g)},na=()=>{a.value=je,a.value.topology.forEach((n,t)=>{n===0&&$e(t)})},ua=()=>{const{targetExpr:n,topology:t}=a.value,p=["target0","target1","target2","target3"],b=["scene0","scene1","scene2","scene3","scene4","scene5"];p.forEach((r,g)=>{a.value[r]=n[g],Y.value[g]=!!n[g]}),b.forEach((r,g)=>{a.value[r]=t[g],M.value[g]=!!t[g]}),Ie.value=re.value.filter((r,g)=>M.value[g]),_e.value=re.value.findIndex((r,g)=>M.value[g])};Ke(A,n=>{});const oa=async()=>{na();const{projectId:n,solutionId:t}=A.query;(n||t)&&(C.loading=!0),await ce(),await N(),await Ae(),Oe(),Be(),Je(),Ye(),la(),t&&(C.loading=!1)},ra=async()=>{var H,U,z,E,ye,oe,he,Ce,we,ke,xe,ve;const{projectId:n,solutionId:t}=A.query;if(n==null){console.log("Pure create project");return}const p={};let b={};p.projectId=parseInt(n),t&&(p.solutionId=parseInt(t)),console.log("enter params22:"),C.loading=!0;const{code:r,msg:g,data:{project:x,solution:f,calcParams:v}}=await ba(p);if(C.loading=!1,console.log("enter params:",x,f,v),b={...x,projectName:x.name},n&&t){const{targetExpr:ae,topology:u}=f;b={...b,...f,...v,target0:ae[0],target1:ae[1],target2:ae[2],target3:ae[3],scene0:u[0],scene1:u[1],scene2:u[2],scene3:u[3],scene4:u[4],scene5:u[5]},T.value=(H=v==null?void 0:v.pv_forecast)==null?void 0:H.data,(z=(U=v==null?void 0:v.pv_forecast)==null?void 0:U.data)!=null&&z.length&&se.value.push({yData:(E=v==null?void 0:v.pv_forecast)==null?void 0:E.data,desc:`自定义数据-${f.name}`,id:-1}),(oe=(ye=v==null?void 0:v.wind_forecast)==null?void 0:ye.data)!=null&&oe.length&&W.value.push({yData:(he=v==null?void 0:v.wind_forecast)==null?void 0:he.data,desc:`自定义数据-${f.name}`,id:-1}),J.value={desc:(Ce=se.value.find(S=>S.id===v.pv_forecast_zone_id))==null?void 0:Ce.desc},ne.value=(we=de.value)==null?void 0:we.find(S=>{var B;return S.id===((B=v==null?void 0:v.pv_dev_ids)==null?void 0:B[0])}),P.value={desc:(ke=W.value.find(S=>S.id===v.wind_forecast_zone_id))==null?void 0:ke.desc},C.selectedBatRowKeys=v.es_dev_ids,C.selectedAlkRowKeys=v.ele_dev_ids,ie.value=[v.grid_zone_id,v.grid_year];const e=(xe=be.value)==null?void 0:xe.find(S=>S.value===v.grid_zone_id),s=(ve=e==null?void 0:e.children)==null?void 0:ve.find(S=>S.value===v.grid_year);ee.value=[e,s],console.log("grid view:",ee.value),C.selectedAlkStoreRowKeys=v.hs_dev_ids}n&&!t&&(b={...je,...x,projectName:x.name}),r==0?a.value=b:me.error(g)};return _a(async()=>{await oa(),await ra(),ua()}),(n,t)=>{var ve,ae;const p=k("a-breadcrumb-item"),b=k("a-breadcrumb"),r=k("a-input-number"),g=k("a-input"),x=k("a-textarea"),f=k("a-form-item"),v=k("a-radio-button"),H=k("a-radio-group"),U=k("a-button"),z=k("a-select-option"),E=k("a-select"),ye=k("a-cascader"),oe=k("a-table"),he=k("a-tag"),Ce=k("a-tab-pane"),we=k("a-tabs"),ke=k("a-form"),xe=k("a-spin");return o(),w("div",Oa,[l("div",Ga,[l("div",null,[_(b,null,{default:i(()=>[_(p,null,{default:i(()=>[l("a",{href:"void:0",onClick:t[0]||(t[0]=u=>d(fe).back())}," < 返回")]),_:1}),_(p,null,{default:i(()=>[D(L(le.value),1)]),_:1})]),_:1})]),l("div",Ja,[_(xe,{spinning:C.loading},{default:i(()=>[_(ke,{ref_key:"formRef",ref:Z,labelAlign:"left",model:a.value,name:"basic","label-col":{span:9},"wrapper-col":{span:12},autocomplete:"off"},{default:i(()=>[l("div",Qa,[Xa,l("div",Ya,[l("div",el,[(o(!0),w(q,null,I(d(Ua)(),u=>(o(),c(f,{class:"line_form_item",label:u.unit?`${u.label}(${u.unit})`:u.label,name:u.name,rules:u.rules},{default:i(()=>[d(A).query.projectId&&["projectName","customer","desc"].includes(u.name)?(o(),w("div",al,L(a.value[u.name]),1)):(o(),w(q,{key:1},[u.type==="number"&&u.numberType==="ratio"?(o(),c(r,{key:0,class:"input_deal_wrap",defaultValue:u.default,value:a.value[u.name],"onUpdate:value":e=>a.value[u.name]=e,size:"small",min:0,max:1,step:.01},null,8,["defaultValue","value","onUpdate:value"])):V("",!0),u.type==="number"&&u.numberType!=="ratio"?(o(),c(r,{key:1,style:{width:"80%"},class:"input_deal_wrap",defaultValue:u.default,value:a.value[u.name],"onUpdate:value":e=>a.value[u.name]=e,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):u.type==="string"?(o(),c(g,{key:2,style:{width:"80%"},class:"input_deal_wrap",defaultValue:u.default,value:a.value[u.name],"onUpdate:value":e=>a.value[u.name]=e,size:"small"},null,8,["defaultValue","value","onUpdate:value"])):u.type==="textarea"?(o(),c(x,{key:3,style:va([{width:"80%"},{margin:"5px 0 5px 0"}]),class:"input_deal_wrap",autosize:{maxRows:3},defaultValue:u.default,value:a.value[u.name],"onUpdate:value":e=>a.value[u.name]=e,size:"small"},null,8,["defaultValue","value","onUpdate:value"])):V("",!0)],64))]),_:2},1032,["label","name","rules"]))),256))])])]),l("div",ll,[tl,l("div",sl,[l("div",nl,[ul,(o(!0),w(q,null,I(d(Ia)(),(u,e)=>(o(),w("div",{onClick:s=>Se(e),name:u.name,rules:u.rules,class:K({sel_item:Y.value[e]})},[l("div",rl,[l("div",il,L(u.label),1),F(l("div",dl,"|",512),[[O,Y.value[e]]]),F(l("div",cl,[_(r,{onChange:s=>Se(e,!0),controls:!1,onClick:t[1]||(t[1]=s=>{s.stopPropagation()}),onStep:t[2]||(t[2]=pa(s=>s.stopPropagation(),["prevent","stop"])),class:"input_deal_wrap",defaultValue:u.default,value:a.value[u.name],"onUpdate:value":s=>a.value[u.name]=s,size:"small",formatter:s=>`${s*100}%`,parser:s=>parseFloat(s.replace("%",""))/100,min:0,max:1},null,8,["onChange","defaultValue","value","onUpdate:value","formatter","parser"])],512),[[O,Y.value[e]]])])],10,ol))),256))]),_l,l("div",vl,[pl,l("div",null,[_(H,{size:"small",value:a.value.algorithm,"onUpdate:value":t[3]||(t[3]=u=>a.value.algorithm=u),"button-style":"solid"},{default:i(()=>[(o(!0),w(q,null,I(d(za)(),u=>(o(),c(v,{value:u.default},{default:i(()=>[D(L(u.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["value"])])])])]),l("div",ml,[fl,l("div",gl,[l("div",bl,[l("div",yl,[l("div",{class:K(["s_t_item_sp",{s_t_item_sel:M.value[0]}]),onClick:t[4]||(t[4]=u=>ue(0))},wl,2),l("div",{class:K(["s_t_item_sp",{s_t_item_sel:M.value[1]}]),onClick:t[5]||(t[5]=u=>ue(1))},Ll,2)]),l("div",{class:K(["s_t_item",{s_t_item_sel:M.value[2]}]),onClick:t[6]||(t[6]=u=>ue(2))},Ul,2),l("div",{class:K(["s_t_item",{s_t_item_sel:M.value[3]}]),onClick:t[7]||(t[7]=u=>ue(3))},Dl,2),l("div",{class:K(["s_t_item",{s_t_item_sel:M.value[4]}]),onClick:t[8]||(t[8]=u=>ue(4))},Tl,2),l("div",{class:K(["s_t_item",{s_t_item_sel:M.value[5]}]),onClick:t[9]||(t[9]=u=>ue(5))},Pl,2)])])]),l("div",Hl,[Zl,l("div",Wl,[l("div",jl,[_(we,{activeKey:_e.value,"onUpdate:activeKey":t[22]||(t[22]=u=>_e.value=u)},{default:i(()=>[(o(!0),w(q,null,I(Ie.value,u=>(o(),c(Ce,{key:u.key,tab:u.title},{default:i(()=>[F(l("div",null,[l("div",El,[_(f,{class:"line_form_item",label:"出力曲线"},{default:i(()=>[l("div",Kl,[_(U,{type:"primary",size:"small",onClick:t[10]||(t[10]=e=>te.value.open())},{default:i(()=>[D(L(J.value.desc?"重新选择":"请选择"),1)]),_:1}),l("div",Al,L(J.value.desc),1)])]),_:1}),_(f,{class:"line_form_item",label:"年衰减率"},{default:i(()=>{var e;return[l("div",Nl,[_(U,{type:"primary",size:"small",onClick:t[11]||(t[11]=s=>ge.value.open())},{default:i(()=>{var s;return[D(L((s=ne.value)!=null&&s.desc?"重新选择":"请选择"),1)]}),_:1}),l("div",Bl,L((e=ne.value)==null?void 0:e.desc),1)])]}),_:1}),_(f,{class:"line_form_item",label:"容量范围(MW)"},{default:i(()=>[l("div",Fl,[_(r,{class:"input_deal_wrap2",controls:!0,defaultValue:0,value:a.value.pv_min_capacity,"onUpdate:value":t[12]||(t[12]=e=>a.value.pv_min_capacity=e),size:"small",min:0},null,8,["value"]),Ol,_(r,{class:"input_deal_wrap2",controls:!0,value:a.value.pv_max_capacity,"onUpdate:value":t[13]||(t[13]=e=>a.value.pv_max_capacity=e),size:"small",min:0},null,8,["value"])])]),_:1}),(o(!0),w(q,null,I(d(Da)(),e=>(o(),c(f,{class:"line_form_item",label:e.unit?`${e.label}(${e.unit})`:e.label,name:e.name,rules:e.rules},{default:i(()=>[e.type==="number"&&e.numberType==="ratio"?(o(),c(r,{key:0,class:"input_deal_wrap",defaultValue:e.default,value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,size:"small",formatter:s=>`${d(y)(s).mul(d(y)(100))}`,parser:s=>d(y)(s).div(d(y)(100)),min:0,max:1,step:.01},null,8,["defaultValue","value","onUpdate:value","formatter","parser"])):V("",!0),e.type==="number"&&e.numberType!=="ratio"?(o(),c(r,{key:1,class:"input_deal_wrap",defaultValue:e.default,value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):e.type==="select"?(o(),c(E,{key:2,ref_for:!0,ref:"select",value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,style:{width:"120px"},onChange:n.handleChange},{default:i(()=>[(o(!0),w(q,null,I(e.options,s=>(o(),c(z,{value:s.value},{default:i(()=>[D(L(s.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value","onChange"])):V("",!0)]),_:2},1032,["label","name","rules"]))),256))])],512),[[O,u.key===0]]),F(l("div",null,[l("div",Gl,[_(f,{class:"line_form_item",label:"出力曲线"},{default:i(()=>[l("div",Jl,[_(U,{type:"primary",size:"small",onClick:t[14]||(t[14]=e=>X.value.open())},{default:i(()=>[D(L(P.value.desc?"重新选择":"请选择"),1)]),_:1}),l("div",Ql,L(P.value.desc),1)])]),_:1}),_(f,{class:"line_form_item",label:"容量范围(MW)"},{default:i(()=>[l("div",Xl,[_(r,{class:"input_deal_wrap2",controls:!1,value:a.value.wind_min_capacity,"onUpdate:value":t[15]||(t[15]=e=>a.value.wind_min_capacity=e),size:"small",min:0},null,8,["value"]),Yl,_(r,{class:"input_deal_wrap2",controls:!1,value:a.value.wind_max_capacity,"onUpdate:value":t[16]||(t[16]=e=>a.value.wind_max_capacity=e),size:"small",min:0},null,8,["value"])])]),_:1}),(o(!0),w(q,null,I(d(Sa)(),e=>(o(),c(f,{class:"line_form_item",label:e.unit?`${e.label}(${e.unit})`:e.label,name:e.name,rules:e.rules},{default:i(()=>[e.type==="number"&&e.numberType==="ratio"?(o(),c(r,{key:0,class:"input_deal_wrap",defaultValue:e.default,value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,size:"small",formatter:s=>`${d(y)(s).mul(d(y)(100))}`,parser:s=>d(y)(s).div(d(y)(100)),min:0,max:1,step:.01},null,8,["defaultValue","value","onUpdate:value","formatter","parser"])):V("",!0),e.type==="number"&&e.numberType!=="ratio"?(o(),c(r,{key:1,class:"input_deal_wrap",defaultValue:e.default,value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):e.type==="select"?(o(),c(E,{key:2,ref_for:!0,ref:"select",value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,style:{width:"120px"},onChange:n.handleChange},{default:i(()=>[(o(!0),w(q,null,I(e.options,s=>(o(),c(z,{value:s.value},{default:i(()=>[D(L(s.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value","onChange"])):V("",!0)]),_:2},1032,["label","name","rules"]))),256))])],512),[[O,u.key===1]]),F(l("div",null,[l("div",et,[(o(!0),w(q,null,I(d(Ma)(),e=>(o(),c(f,{class:"line_form_item",label:e.unit?`${e.label}(${e.unit})`:e.label,name:e.name,rules:e.rules},{default:i(()=>[e.type==="number"&&e.numberType==="ratio"?(o(),c(r,{key:0,class:"input_deal_wrap",defaultValue:e.default,value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,size:"small",formatter:s=>`${d(y)(s).mul(d(y)(100))}`,parser:s=>d(y)(s).div(d(y)(100)),min:0,max:1,step:.01},null,8,["defaultValue","value","onUpdate:value","formatter","parser"])):V("",!0),e.type==="number"&&e.numberType!=="ratio"?(o(),c(r,{key:1,class:"input_deal_wrap",defaultValue:e.default,value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):e.type==="select"?(o(),c(E,{key:2,ref_for:!0,ref:"select",value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,style:{width:"120px"},onChange:n.handleChange},{default:i(()=>[(o(!0),w(q,null,I(e.options,s=>(o(),c(z,{value:s.value},{default:i(()=>[D(L(s.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value","onChange"])):V("",!0)]),_:2},1032,["label","name","rules"]))),256)),_(f,{class:"line_form_item",label:"网购电价",name:"item.name",rules:"item.rules"},{default:i(()=>{var e,s,S,B,pe;return[l("div",at,[_(ye,{value:ie.value,"onUpdate:value":t[17]||(t[17]=Le=>ie.value=Le),placeholder:"Please select",options:be.value,onChange:Ge},{default:i(()=>[_(U,{type:"primary",size:"small"},{default:i(()=>{var Le,Re;return[D(L((Re=(Le=ee.value)==null?void 0:Le[1])!=null&&Re.label?"重新选择":"请选择"),1)]}),_:1})]),_:1},8,["value","options"]),(e=ee.value)!=null&&e[1]?(o(),w("div",lt,L((S=(s=ee.value)==null?void 0:s[0])==null?void 0:S.label)+", "+L((pe=(B=ee.value)==null?void 0:B[1])==null?void 0:pe.label),1)):V("",!0)])]}),_:1})])],512),[[O,u.key===2]]),F(l("div",null,[l("div",tt,[(o(!0),w(q,null,I(d(Ra)(),e=>(o(),c(f,{class:"line_form_item",label:e.unit?`${e.label}(${e.unit})`:e.label,name:e.name,rules:e.rules},{default:i(()=>[e.type==="number"&&e.numberType==="ratio"?(o(),c(r,{key:0,class:"input_deal_wrap",defaultValue:e.default,value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,size:"small",formatter:s=>`${d(y)(s).mul(d(y)(100))}`,parser:s=>d(y)(s).div(d(y)(100)),min:0,max:1,step:.01},null,8,["defaultValue","value","onUpdate:value","formatter","parser"])):V("",!0),e.type==="number"&&e.numberType!=="ratio"?(o(),c(r,{key:1,class:"input_deal_wrap",defaultValue:e.default,value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):e.type==="select"?(o(),c(E,{key:2,size:"small",ref_for:!0,ref:"select",value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,style:{width:"100px"},onChange:n.handleChange},{default:i(()=>[(o(!0),w(q,null,I(e.options,s=>(o(),c(z,{value:s.value},{default:i(()=>[D(L(s.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value","onChange"])):V("",!0)]),_:2},1032,["label","name","rules"]))),256)),_(f,{class:"line_form_item",label:"容量范围(MWh)"},{default:i(()=>[l("div",st,[_(r,{class:"input_deal_wrap2",value:a.value.es_min_capacity,"onUpdate:value":t[18]||(t[18]=e=>a.value.es_min_capacity=e),size:"small",min:0},null,8,["value"]),nt,_(r,{class:"input_deal_wrap2",value:a.value.es_max_capacity,"onUpdate:value":t[19]||(t[19]=e=>a.value.es_max_capacity=e),size:"small",min:0},null,8,["value"])])]),_:1})]),l("div",ut,[ot,_(oe,{class:"i_val",rowKey:"id","row-selection":{selectedRowKeys:C.selectedBatRowKeys,onChange:Qe,type:"radio"},columns:d(Pa)(),"data-source":$.value.bat,pagination:!1,size:"small"},null,8,["row-selection","columns","data-source"])])],512),[[O,u.key===3]]),F(l("div",null,[l("div",rt,[(o(!0),w(q,null,I(d($a)(),e=>(o(),c(f,{class:"line_form_item",label:e.unit?`${e.label}(${e.unit})`:e.label,name:e.name,rules:e.rules},{default:i(()=>[e.type==="number"&&e.numberType==="ratio"?(o(),c(r,{key:0,class:"input_deal_wrap",defaultValue:e.default,value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,size:"small",formatter:s=>`${d(y)(s).mul(d(y)(100))}`,parser:s=>d(y)(s).div(d(y)(100)),min:0,max:1,step:.01},null,8,["defaultValue","value","onUpdate:value","formatter","parser"])):V("",!0),e.type==="number"&&e.numberType!=="ratio"?(o(),c(r,{key:1,class:"input_deal_wrap",defaultValue:e.default,value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):e.type==="select"?(o(),c(E,{key:2,ref_for:!0,ref:"select",value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,style:{width:"120px"},onChange:n.handleChange},{default:i(()=>[(o(!0),w(q,null,I(e.options,s=>(o(),c(z,{value:s.value},{default:i(()=>[D(L(s.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value","onChange"])):V("",!0)]),_:2},1032,["label","name","rules"]))),256)),_(f,{class:"line_form_item",label:"容量范围(MW)"},{default:i(()=>[l("div",it,[_(r,{class:"input_deal_wrap2",value:a.value.ele_min_capacity,"onUpdate:value":t[20]||(t[20]=e=>a.value.ele_min_capacity=e),size:"small",min:0},null,8,["value"]),dt,_(r,{class:"input_deal_wrap2",value:a.value.ele_max_capacity,"onUpdate:value":t[21]||(t[21]=e=>a.value.ele_max_capacity=e),size:"small",min:0},null,8,["value"])])]),_:1})]),l("div",ct,[_t,_(oe,{class:"i_val",rowKey:"id","row-selection":{selectedRowKeys:C.selectedAlkRowKeys,onChange:Ne,getCheckboxProps:Fe},columns:d(Ta)(),"data-source":$.value.alk,pagination:!1,size:"small"},{bodyCell:i(({column:e,record:s,text:S})=>{var B;return[e.key==="ele_type"?(o(),c(he,{key:0,color:(B=d(Ze)(e.options,S))==null?void 0:B.color},{default:i(()=>{var pe;return[D(L((pe=d(Ze)(e.options,S))==null?void 0:pe.label),1)]}),_:2},1032,["color"])):V("",!0)]}),_:1},8,["row-selection","columns","data-source"])])],512),[[O,u.key===4]]),F(l("div",null,[l("div",vt,[(o(!0),w(q,null,I(d(Ha)(),e=>(o(),c(f,{class:"line_form_item",label:e.unit?`${e.label}(${e.unit})`:e.label,name:e.name,rules:e.rules},{default:i(()=>[e.type==="number"&&e.numberType==="ratio"?(o(),c(r,{key:0,class:"input_deal_wrap",defaultValue:e.default,value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,size:"small",formatter:s=>`${d(y)(s).mul(d(y)(100))}`,parser:s=>d(y)(s).div(d(y)(100)),min:0,max:1,step:.01},null,8,["defaultValue","value","onUpdate:value","formatter","parser"])):V("",!0),e.type==="number"&&e.numberType!=="ratio"?(o(),c(r,{key:1,class:"input_deal_wrap",defaultValue:e.default,value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):e.type==="select"?(o(),c(E,{key:2,ref_for:!0,ref:"select",value:a.value[e.name],"onUpdate:value":s=>a.value[e.name]=s,style:{width:"120px"},onChange:n.handleChange},{default:i(()=>[(o(!0),w(q,null,I(e.options,s=>(o(),c(z,{value:s.value},{default:i(()=>[D(L(s.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value","onChange"])):V("",!0)]),_:2},1032,["label","name","rules"]))),256))]),l("div",pt,[mt,_(oe,{class:"i_val",rowKey:"id","row-selection":{selectedRowKeys:C.selectedAlkStoreRowKeys,onChange:aa,type:"radio"},columns:d(Za)(),"data-source":$.value.alkStorage,pagination:!1,size:"small"},null,8,["row-selection","columns","data-source"])])],512),[[O,u.key===5]])]),_:2},1032,["tab"]))),128))]),_:1},8,["activeKey"])])])]),l("div",ft,[gt,l("div",bt,[l("div",yt,[(o(!0),w(q,null,I(d(Ea)(),u=>(o(),c(f,{class:"line_form_item",label:u.unit?`${u.label}(${u.unit})`:u.label,name:u.name,rules:u.rules},{default:i(()=>[u.type==="number"&&u.numberType==="ratio"?(o(),c(r,{key:0,class:"input_deal_wrap",defaultValue:u.default,value:a.value[u.name],"onUpdate:value":e=>a.value[u.name]=e,size:"small",formatter:e=>`${d(y)(e||0).mul(d(y)(100))}`,parser:e=>d(y)(e||0).div(d(y)(100)),min:0,max:1,step:.01},null,8,["defaultValue","value","onUpdate:value","formatter","parser"])):V("",!0),u.type==="number"&&u.numberType!=="ratio"?(o(),c(r,{key:1,class:"input_deal_wrap",defaultValue:u.default,value:a.value[u.name],"onUpdate:value":e=>a.value[u.name]=e,size:"small",min:0},null,8,["defaultValue","value","onUpdate:value"])):V("",!0)]),_:2},1032,["label","name","rules"]))),256))])])])]),_:1},8,["model"])]),_:1},8,["spinning"])])]),l("div",ht,[_(U,{onClick:sa,type:"primary",block:"",loading:C.running},{default:i(()=>[D("运行")]),_:1},8,["loading"])]),_(qe,{chartList:se.value,config:{modalTitle:"光伏出力曲线",hasZoom:!0,canUploadSelf:!0,default:{id:a.value.pv_forecast_zone_id}},ref_key:"PVPowerModalRef",ref:te,onSubmit:R},null,8,["chartList","config"]),_(qe,{chartList:de.value,selfData:T.value,config:{modalTitle:"光伏设备衰减曲线",hasZoom:!1,canUploadSelf:!1,default:{id:(ae=(ve=a.value)==null?void 0:ve.pv_dev_ids)==null?void 0:ae[0]}},ref_key:"PVDeviceModalRef",ref:ge,onSubmit:Q},null,8,["chartList","selfData","config"]),_(qe,{chartList:W.value,config:{modalTitle:"风机出力曲线",hasZoom:!0,canUploadSelf:!0,default:{id:a.value.wind_forecast_zone_id}},ref_key:"WindPowerModalRef",ref:X,onSubmit:De},null,8,["chartList","config"]),_(qe,{chartList:Te.value,config:{modalTitle:"需氢曲线",hasZoom:!0,canUploadSelf:!1,default:{id:a.value.absorb_id}},ref_key:"H2ConsumeModalRef",ref:Me,onSubmit:ea},null,8,["chartList","config"])])}}},It=Ee(Ct,[["__scopeId","data-v-a96c4f0d"]]);export{It as default};
