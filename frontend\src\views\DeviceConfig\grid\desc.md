新加一个电价模块，主界面如图-主视图：

点击左上角新增按钮，会出现从右侧出现的滑窗，如图-滑窗1，可以出现下拉选择地区(省市两级)，也可输入自定义地区(只有一级)

电价区间，分为年(1-12月)，半年(1-6月，7-12月两部分)，季度(1-3月，4-6月，7-9月，10-12月四部分)，按月(12个月，12部分)。点击右下角确认，到主视图。选中季度的话，上方标签为4部分，分成4个季节，每个季节对应各自月份，其他类似。选中月份标签后，下方可以配置每天电价，设每个月份标签下的电价每天都一样。

电解试图右上角点击添加，出现如图-滑窗2，可以添加类型，价格，时间区域，可以一次添加多个。已经选择了的小时，在其他时段不能重复选择，可以置灰已选的时间。
类型：5-尖峰，4-高峰，3-平时，2-低谷，1-深谷
时间区域：为0-24点的小时

对于24小时的电价图，不同类型电价不同颜色，配置好电价后，可以选中(点击梯度图选中)某一时间区域的电价，画出右侧滑窗编辑点击，确认后保存。

数据格式(数据可以先 mock 生成)：
地区数据：
```js
{
  total: 10,
  result: [
    {
      id: 10,
      region: '区域',
      country: '中国',
      province: '省份',
      subZones: [
        { 
          id: 11,
          region: '区域',
          country: '中国',
          province: '省份',
          city: '城市1'
        },
        { 
          id: 12,
          region: '区域',
          country: '中国',
          province: '省份',
          city: '城市2'
        }
      ]
    }
  ]
}

```

电价数据(提交，更新都用如下结构，每次提交全量数据)：
```js
// 列表响应：
{
  code: 0,
  data: { total: 10, result: item}
}
// 提交更新,如 item：
const item = {
  zone: {
    id: 10,
    region: '',
    country: '',
    province: '省份',// 自定义地区的地区名，写入这里。否则按获取的地区数据写入。
    city:'城市',
  },
  price: {
    2024: [
      {
        begin: 1,  // 起始月份，最小1
        end: 6,  // 终止月份，最大 12
        groupedTouPriceByDay: [
          {
            begin: 0, // 起始小时，最小 0
            end: 12, // 终止小时，最大23
            price: 0.5,
            type:1 // 电价类型： 5-尖峰，4-高峰，3-平时，2-低谷，1-深谷
          },
          {
            begin: 13, // 起始小时，最小 0
            end: 23, // 终止小时，最大23
            price: 0.6,
            type:1 // 电价类型： 5-尖峰，4-高峰，3-平时，2-低谷，1-深谷
          }
        ]
      },
      {
        begin: 7, // 起始月份，最小1
        end: 12, // 终止月份，最大 12
        groupedTouPriceByDay: [
          {
            begin: 0, // 起始小时，最小 0
            end: 23, // 终止小时，最大23
            price: 1.2,
            type:1 // 电价类型： 5-尖峰，4-高峰，3-平时，2-低谷，1-深谷
          }
        ]
      }
    ]
  }
}
```