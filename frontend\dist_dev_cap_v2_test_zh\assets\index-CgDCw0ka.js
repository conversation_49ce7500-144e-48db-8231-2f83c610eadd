import{N as s,W as e,O as t}from"./index-mrm9ST-C.js";const n=async()=>await s(`${t}/capacity/getSolutionTypes`),o=async a=>await s(`${t}/capacity/getSolutionList`,a),i=async a=>await s(`${t}/capacity/getProject`,a),r=async a=>await s(`${t}/capacity/getResult`,a),u=async a=>await s(`${t}/capacity/getSolution`,a),y=async a=>await s(`${t}/config/getForecast`,a),g=async a=>await s(`${t}/config/getDevParams`,a),l=async()=>await s(`${t}/config/getH2AbsorbConfig`),m=async a=>await e(`${t}/capacity/submitTask`,a),d=async a=>await e(`${t}/capacity/saveSolution`,a),p=async a=>await e(`${t}/capacity/mdfCalcParams`,a),w=async a=>await e(`${t}/capacity/modifyProject`,a),$=async a=>await e(`${t}/config/addDevParams`,a),P=async a=>await e(`${t}/config/updateDevParams`,a),f=async a=>await e(`${t}/config/deleteDevParams`,a),v=async a=>await e(`${t}/capacity/deleteSolution`,a),S=async a=>await s(`${t}/config/getGridPrice`,a);export{n as a,o as b,r as c,g as d,m as e,u as f,i as g,y as h,S as i,l as j,w as k,$ as l,p as m,f as n,v as o,d as s,P as u};
