import{Q as v,r as g,d as b,x as h,f as o,b as x,c as y,g as d,h as e,w as t,n as I,R as S,A as B,D as C,E as N,_ as V}from"./index-mrm9ST-C.js";const k=n=>(C("data-v-275e3a6f"),n=n(),N(),n),q={class:"bg_wrap"},E={class:"login_window"},R=k(()=>d("div",{class:"title"},"综合能源规划设计系统",-1)),U=v({__name:"index",setup(n){const r=g(!1),_=b(),s=h({username:"",password:""}),i=async c=>{r.value=!0;const{code:a,msg:l}=await S(c);r.value=!1,a===0?(console.log("login success"),_.push({name:"home"})):B.error(l)};return(c,a)=>{const l=o("a-input"),p=o("a-form-item"),m=o("a-input-password"),f=o("a-button"),w=o("a-form");return x(),y("div",q,[d("div",E,[R,e(w,{class:"form_wrap",model:s,name:"basic","label-col":{span:6},"wrapper-col":{span:14},autocomplete:"off",onFinish:i},{default:t(()=>[e(p,{label:"用户名",name:"username",rules:[{required:!0,message:"请输入用户名!"}]},{default:t(()=>[e(l,{value:s.username,"onUpdate:value":a[0]||(a[0]=u=>s.username=u)},null,8,["value"])]),_:1}),e(p,{label:"密码",name:"password",rules:[{required:!0,message:"请输入密码!"}]},{default:t(()=>[e(m,{value:s.password,"onUpdate:value":a[1]||(a[1]=u=>s.password=u)},null,8,["value"])]),_:1}),e(p,{"wrapper-col":{offset:6,span:14}},{default:t(()=>[e(f,{loading:r.value,style:{width:"100%"},type:"primary","html-type":"submit"},{default:t(()=>[I(" 登录 ")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])])])}}}),D=V(U,[["__scopeId","data-v-275e3a6f"]]);export{D as default};
