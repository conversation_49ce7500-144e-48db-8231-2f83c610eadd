import{e as B}from"./index-DIXaVCf5.js";import{_ as V,u as M,r as u,o as T,d,b as i,c as I,e as o,f as l,w as a,y as L,l as f,j as E,h as K,k as P,p as F,m as G}from"./index-D4CjQwBG.js";import{l as H}from"./lodash-BnsPkmok.js";/* empty css                                                              */import"./index-CNS3ZdVi.js";const J=()=>[{title:"单机功率",dataIndex:"single_power"},{title:"衰减曲线",dataIndex:"damp_curve"},{title:"充电效率",dataIndex:"charge_efficiency"},{title:"放电效率",dataIndex:"discharge_efficiency"},{title:"充放电倍率",dataIndex:"c_rate"},{title:"储能占比",dataIndex:"radio"},{title:"初始SOC",dataIndex:"init_soc"},{title:"SOC下限",dataIndex:"min_soc"},{title:"SOC上限",dataIndex:"max_soc"},{title:"置信度",dataIndex:"confidence"},{title:"寿命",dataIndex:"life_cycle"},{title:"项目操作",dataIndex:"action",key:"action"}],Q=()=>[{title:"电解槽类型",dataIndex:"type"},{title:"容量",dataIndex:"capacity"},{title:"能耗",dataIndex:"power_consumption"},{title:"额定功率",dataIndex:"pe"},{title:"最低负载率",dataIndex:"lower_load_rate"},{title:"最高负载率",dataIndex:"upper_load_rate"},{title:"衰减曲线",dataIndex:"damp_curve"},{title:"辅助系统能耗",dataIndex:"assist_consumption"},{title:"单价",dataIndex:"price"},{title:"制氢电源效率",dataIndex:"power_supply_efficiency"},{title:"项目操作",dataIndex:"action",key:"action"}],W=()=>[{title:"体积",dataIndex:"volume"},{title:"最低运行压力",dataIndex:"min_pressure"},{title:"最大运行压力",dataIndex:"max_pressure"},{title:"价格",dataIndex:"price"},{title:"占地面积",dataIndex:"area"},{title:"项目操作",dataIndex:"action",key:"action"}],R=x=>(F("data-v-cfbc87fb"),x=x(),G(),x),X={class:"body_wrap"},Y={class:"p_wrap"},Z=R(()=>o("div",{class:"title_wrap"},null,-1)),ee={class:"content_wrap",id:"content_wrap"},te={class:"part_wrap"},ae=R(()=>o("div",{class:"p_title"},[o("div",null,"设备配置"),o("div",{class:"btn_wrap"})],-1)),oe={class:"tab_wrap"},ne={key:0,class:"t_btn_wrap"},le=["onClick"],se=["onClick"],de={key:0,class:"t_btn_wrap"},ie=["onClick"],ce=["onClick"],re={key:0,class:"t_btn_wrap"},ue=["onClick"],_e=["onClick"],pe={__name:"index",setup(x){const j=M(),_=u(!1),y=u(!1),D=u(),p=u([]),c=u({}),N=u({}),O=u(!1),k=u(3),g=(n,e)=>{console.log("ceateSolution:",n,e);const s={projectId:n.id};(e==null?void 0:e.id)!==void 0&&(s.solutionId=e.id),j.push({name:"createProject",query:s})},w=n=>{y.value=!0,N.value=n,c.value=H.cloneDeep(n)},U=n=>{k.value=n,$({type:n})},$=async n=>{_.value=!0;const{code:e,data:s,msg:C}=await B(n);e===0?(p.value=s.result.map(m=>{const{baseInfo:h,params:b}=m;return{...h,...b}}),console.log("table:",p.value)):L.error(C),_.value=!1};return T(()=>{$()}),(n,e)=>{const s=d("a-tab-pane"),C=d("a-button"),m=d("a-table"),h=d("a-tabs"),b=d("a-input"),S=d("a-form-item"),q=d("a-textarea"),z=d("a-form"),A=d("a-modal");return i(),I("div",X,[o("div",Y,[Z,o("div",ee,[o("div",te,[ae,o("div",oe,[l(h,{activeKey:k.value,"onUpdate:activeKey":e[1]||(e[1]=t=>k.value=t),onChange:U},{default:a(()=>[(i(),f(s,{key:1,tab:"光伏"},{default:a(()=>[E(" 光伏 ")]),_:1})),(i(),f(s,{key:2,tab:"风机"},{default:a(()=>[E(" 风机 ")]),_:1})),(i(),f(s,{key:3,tab:"储能"},{default:a(()=>[l(C,{style:{margin:"-10px 0 10px 0"},size:"small",onClick:e[0]||(e[0]=t=>n.createNewItem(3))},{default:a(()=>[E("新增")]),_:1}),l(m,{size:"small",loading:_.value,pagination:!1,columns:K(J)(),rowKey:"id","data-source":p.value,defaultExpandAllRows:!0},{bodyCell:a(({column:t,record:r})=>[t.key==="action"?(i(),I("div",ne,[o("a",{href:"void:0",class:"a_item",onClick:v=>w(r)},"修改",8,le),o("a",{href:"void:0",class:"a_item",onClick:v=>g(r)},"删除",8,se)])):P("",!0)]),_:1},8,["loading","columns","data-source"])]),_:1})),(i(),f(s,{key:4,tab:"电解槽"},{default:a(()=>[l(m,{size:"small",loading:_.value,pagination:!1,columns:K(Q)(),rowKey:"id","data-source":p.value,defaultExpandAllRows:!0},{bodyCell:a(({column:t,record:r})=>[t.key==="action"?(i(),I("div",de,[o("a",{href:"void:0",class:"a_item",onClick:v=>w(r)},"修改",8,ie),o("a",{href:"void:0",class:"a_item",onClick:v=>g(r)},"删除",8,ce)])):P("",!0)]),_:1},8,["loading","columns","data-source"])]),_:1})),(i(),f(s,{key:5,tab:"储罐"},{default:a(()=>[l(m,{size:"small",loading:_.value,pagination:!1,columns:K(W)(),rowKey:"id","data-source":p.value,defaultExpandAllRows:!0},{bodyCell:a(({column:t,record:r})=>[t.key==="action"?(i(),I("div",re,[o("a",{href:"void:0",class:"a_item",onClick:v=>w(r)},"修改",8,ue),o("a",{href:"void:0",class:"a_item",onClick:v=>g(r)},"删除",8,_e)])):P("",!0)]),_:1},8,["loading","columns","data-source"])]),_:1}))]),_:1},8,["activeKey"])])])])]),l(A,{open:y.value,"onUpdate:open":e[5]||(e[5]=t=>y.value=t),title:"修改项目",onOk:n.submitEditProject,"confirm-loading":O.value},{default:a(()=>[o("div",null,[l(z,{labelAlign:"left2",ref_key:"formRef",ref:D,model:c.value,name:"basic","label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:a(()=>[l(S,{label:"项目名称",name:"name",rules:[{required:!0,message:"Please input!"}]},{default:a(()=>[l(b,{value:c.value.name,"onUpdate:value":e[2]||(e[2]=t=>c.value.name=t)},null,8,["value"])]),_:1}),l(S,{label:"客户名称",name:"customer",rules:[{required:!0,message:"Please input!"}]},{default:a(()=>[l(b,{value:c.value.customer,"onUpdate:value":e[3]||(e[3]=t=>c.value.customer=t)},null,8,["value"])]),_:1}),l(S,{label:"项目背景",name:"desc",rules:[{required:!0,message:"Please input!"}]},{default:a(()=>[l(q,{value:c.value.desc,"onUpdate:value":e[4]||(e[4]=t=>c.value.desc=t)},null,8,["value"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["open","onOk","confirm-loading"])])}}},Ie=V(pe,[["__scopeId","data-v-cfbc87fb"]]);export{Ie as default};
