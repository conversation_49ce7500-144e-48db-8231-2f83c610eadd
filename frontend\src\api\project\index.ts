// import { AxiosPromise} from 'axios'
import { get, post, urlPrefix, type Res, del } from '../index'

export interface TaskItem {
  [key: string]: any // TODO
}

export interface PolicyItem {
  id: number;
  name: string;
  status: number; // 0 未启用；1 启用
  desc: string;
  // 以下暂时自定义
  score: number;
  creator: string; // 定制人
  period: string; // 定制周期
}

export interface PlanItem {
  id: number;
  dev_id: number; // 1-6. 1-4 ALK, 5-PV, 6-SOC, 8-Load
  prop_id: number;
  type: number;
  interval: number;
  data: number[];
  begin_time: string;
  end_time: string;
  create_time: string;
  desc: string;
}

// export const submitTask = async (params: TaskItem): Promise<Res<any>> => {
//   return await post(`${urlPrefix}/gsct/submitTask`, params, {
//     'Content-Type' : 'multipart/form-data'
//   });
// }

// export const createPolicy = async (params: PolicyItem): Promise<Res<any>> => {
//   return await post(`${urlPrefix}/policyManager/policy`, params);
// }

export const getSolutionTypes = async (): Promise<Res<PolicyItem[]>> => {
  return await get(`${urlPrefix}/capacity/getSolutionTypes`);
}

export const getSolutionList = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await get(`${urlPrefix}/capacity/getSolutionList`, params);
}

export const getProjects = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await get(`${urlPrefix}/capacity/getProject`, params);
}

export const getResult = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await get(`${urlPrefix}/capacity/getResult`, params);
}
export const getSolution = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await get(`${urlPrefix}/capacity/getSolution`, params);
}

export const getForecast = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await get(`${urlPrefix}/config/getForecast`, params);
}

export const getDevice = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await get(`${urlPrefix}/config/getDevParams`, params);
}

export const getH2Consume = async (): Promise<Res<PolicyItem[]>> => {
  return await get(`${urlPrefix}/config/getH2AbsorbConfig`);
}

export const submitTask = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await post(`${urlPrefix}/capacity/submitTask`, params);
}

export const saveSolution = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await post(`${urlPrefix}/capacity/saveSolution`, params);
}

export const mdfCalcParams = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await post(`${urlPrefix}/capacity/mdfCalcParams`, params);
}

export const modifyProject = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await post(`${urlPrefix}/capacity/modifyProject`, params);
}

export const addDevParams = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await post(`${urlPrefix}/config/addDevParams`, params);
}

export const updateDevParams = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await post(`${urlPrefix}/config/updateDevParams`, params);
}

export const deleteDevParams = async (params: any): Promise<Res<PolicyItem[]>> => {
  return await post(`${urlPrefix}/config/deleteDevParams`, params);
}

export const deleteProjects = async (params: any): Promise<Res<any>> => {
  return await post(`${urlPrefix}/capacity/deleteProject`, params);
}

export const deleteSoutions = async (params: any): Promise<Res<any>> => {
  return await post(`${urlPrefix}/capacity/deleteSolution`, params);
}

export const getZone = async (params: any): Promise<Res<any>> => {
  return await get(`${urlPrefix}/config/getZone`, params);
}

export const getGridPrice = async (params?: any): Promise<Res<any>> => {
  return await get(`${urlPrefix}/config/getGridPrice`, params);
}

export const updateGridPrice = async (params: any): Promise<Res<any>> => {
  return await post(`${urlPrefix}/config/updateGridPrice`, params);
}


// export const deletePolicy = async (id: number ): Promise<Res<any>> => {
//   return await del(`${urlPrefix}/policyManager/policy/${id}/delete`);
// }
