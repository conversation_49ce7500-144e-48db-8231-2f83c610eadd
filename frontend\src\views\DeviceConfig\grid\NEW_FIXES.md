# 电价配置模块新问题修复

## 修复的问题

### 1. 新建地区后不直接提交数据

**问题描述**：
- 之前新建地区后立即调用API提交数据
- 应该等用户编辑完电价后再提交

**修复方案**：

#### 1.1 新建地区时只在本地创建
```javascript
const handleRegionConfirm = (regionData) => {
  const newConfig = {
    ...regionData,
    id: regionData.zoneId || Date.now(),
    currentYear: new Date().getFullYear().toString(),
    createTime: Date.now(),
    isNew: true // 标记为新建地区，尚未保存到后端
  }

  // 只在本地添加，不立即提交到后端
  priceConfigs.value.push(newConfig)
  selectedKeys.value = [newConfig.id.toString()]
  showRegionModal.value = false

  message.success('地区添加成功，请配置电价后保存')
}
```

#### 1.2 编辑电价后才提交数据
```javascript
// 在saveTimeEdit函数中
// 调用API更新数据到后端
const updateSuccess = await updateConfigedZone(config)
if (!updateSuccess) {
  return // 如果更新失败，不关闭弹窗
}

// 如果是新建地区，保存成功后移除isNew标记
if (config.isNew) {
  delete config.isNew
  priceConfigs.value[configIndex] = { ...config }
}
```

#### 1.3 界面显示未保存状态
```vue
<h3>
  {{ currentConfig.province }}{{ currentConfig.city ? '-' + currentConfig.city : '' }}
  <a-tag v-if="currentConfig.isNew" color="orange" style="margin-left: 8px">
    未保存
  </a-tag>
</h3>
```

#### 1.4 删除逻辑优化
```javascript
const deleteRegion = async (configId) => {
  const config = priceConfigs.value[index]
  
  // 如果是新建但未保存的地区，直接删除
  if (config.isNew) {
    priceConfigs.value.splice(index, 1)
    selectedKeys.value = []
    
    // 删除后如果还有其他地区，选中第一个
    if (priceConfigs.value.length > 0) {
      selectedKeys.value = [priceConfigs.value[0].id.toString()]
    }
    
    message.success('删除成功')
    return
  }
  
  // 已保存的地区需要调用API删除
  // ...
}
```

### 2. 默认展示第一个地区

**问题描述**：
- 页面加载后没有默认选中任何地区
- 用户需要手动点击才能查看电价配置

**修复方案**：

#### 2.1 获取数据后默认选中第一个地区
```javascript
const getConfigedZone = async() => {
  // ...获取和转换数据
  
  // 默认选中第一个地区
  if (priceConfigs.value.length > 0) {
    selectedKeys.value = [priceConfigs.value[0].id.toString()]
    console.log('默认选中第一个地区:', priceConfigs.value[0])
  }
}
```

#### 2.2 树形数据更新时的默认选中
```javascript
// 监听regionTreeData变化，自动展开所有节点
watch(regionTreeData, (newData) => {
  // ...展开节点逻辑
  
  // 如果还没有选中任何地区，且有数据，则默认选中第一个
  if (selectedKeys.value.length === 0 && priceConfigs.value.length > 0) {
    selectedKeys.value = [priceConfigs.value[0].id.toString()]
    console.log('树形数据更新后默认选中第一个地区:', priceConfigs.value[0])
  }
}, { immediate: true })
```

#### 2.3 删除地区后的自动选中
```javascript
// 删除后如果还有其他地区，选中第一个
if (priceConfigs.value.length > 0) {
  selectedKeys.value = [priceConfigs.value[0].id.toString()]
}
```

## 用户体验改进

### 1. 新建地区流程
1. 用户点击"新增"按钮
2. 选择或输入地区信息
3. 地区添加到列表，显示"未保存"标签
4. 用户编辑电价配置
5. 点击"确认"保存电价时，同时提交地区和电价数据
6. 保存成功后移除"未保存"标签

### 2. 默认显示流程
1. 页面加载，获取地区列表
2. 自动选中第一个地区
3. 显示该地区的电价配置
4. 用户可以直接查看和编辑

### 3. 删除地区流程
1. 新建未保存的地区：直接删除，不调用API
2. 已保存的地区：调用API删除（目前注释状态）
3. 删除后自动选中剩余的第一个地区

## 数据状态管理

### 地区配置对象结构
```javascript
{
  id: 123,
  zoneId: 123,
  region: '华北地区',
  country: '中国',
  province: '北京市',
  city: '海淀区',
  currentYear: '2024',
  period: 'quarter',
  createTime: 1234567890,
  isNew: true, // 新建未保存的地区才有此字段
  groupedTouPriceByMonth: [...]
}
```

### 状态标识
- `isNew: true`：新建但未保存到后端的地区
- 无`isNew`字段：已保存到后端的地区

## 测试建议

1. **新建地区测试**：
   - 新建地区后检查是否显示"未保存"标签
   - 编辑电价后保存，检查标签是否消失
   - 新建地区后直接删除，检查是否不调用API

2. **默认选中测试**：
   - 刷新页面，检查是否自动选中第一个地区
   - 删除当前选中地区，检查是否自动选中下一个

3. **数据一致性测试**：
   - 新建地区编辑电价后保存，检查数据是否正确提交
   - 已有地区编辑电价后保存，检查数据是否正确更新

## 注意事项

1. 新建地区的ID使用时间戳生成，确保唯一性
2. 删除API调用目前处于注释状态，需要后端提供接口时启用
3. 调试信息在生产环境中应该移除
4. `isNew`标记只在前端使用，不会提交到后端
